workspace = { members = ["lib/contracts", "lib/db", "lib/redis_2"] }

[package]
authors = ["d<PERSON><PERSON> b<PERSON>"]
name = "dpn-admin"
version = "0.1.0"
edition = "2021"

[profile.release]
opt-level = 3
debug = 1  # Include line tables for panic backtraces
split-debuginfo = "packed"  # Better for distribution
debug-assertions = false
overflow-checks = true  # Keep these on for safety
lto = "thin"  # Good compromise between build time and optimization
panic = 'unwind'
incremental = false
codegen-units = 16  # Better build times while still allowing good optimization
rpath = false


[[bin]]
name = "admin"
path = "src/main.rs"

[dependencies]
dpn_core = { git = "https://github.com/unicornultralabs/subnet-dpn-core", branch = "main" }

dpn_db = { path = "./lib/db" }
dpn_redis_2 = { path = "./lib/redis_2" }
# dpn_contracts = { path = "./lib/contracts" }

tokio = { version = "1.32.0", features = ["full"] }
anyhow = "1.0.75"
reqwest = { version = "0.11.18", features = ["json", "native-tls-crate"] }
serde = { version = "1.0.183", features = ["derive"] }
serde_json = "1.0.105"
static_init = "1.0.3"
actix-web = "4.3.1"
env_logger = "0.10.0"
log = "0.4.20"
actix-cors = "0.6.4"
num-traits = "0.2.17"
num-derive = "0.4.1"
ethers = "2.0.10"
futures-util = "0.3.30"
tokio-tungstenite = "0.21.0"
base64 = "0.13.0"
jwks-client = "0.1.5"
rustls = { version = "0.23.23", features = ["ring"] }
regex = "1.11.1"
[target.'cfg(not(target_env = "msvc"))'.dependencies]
serde_yaml = "0.9.25"
async-trait = "0.1.73"
graphql_client = "0.13.0"
mockall = { version = "0.11.2", features = ["nightly"] }
sqlx = { version = "0.7.3", default-features = false, features = ["chrono"] }
num = { version = "0.3.1", features = ["serde"] }
jsonwebtoken = "9.2.0"
actix-web-httpauth = "0.8.1"
utoipa = { version = "4.1.0", features = ["actix_extras"] }
utoipa-rapidoc = { version = "2.0.0", features = ["actix-web"] }
utoipa-redoc = { version = "2.0.0", features = ["actix-web"] }
utoipa-swagger-ui = { version = "5.0.0", features = ["actix-web"] }
rand = "0.8"
maxminddb = "0.17"
futures-lite = "2.2.0"
argon2 = "0.5.3"
prometheus = { version = "0.13.0", features = ["process"] }
actix-web-prom = "0.8.0"
actix-service = "2.0.2"
lazy_static = "1.4.0"
mime = "0.3.16"
lapin = "2.3.1"
moka = { version = "0.12", features = ["future"] }
dashmap = "6.0.1"
once_cell = "1.19.0"
uuid = { version = "1.4", features = ["v4", "serde"] }
bb8-redis = { version = "0.20.0" }
futures = "0.3.30"
