# Proxies
Masternode supports http(s) and socks5.

Usage: 

set config.yaml and run

username format: (country code in alpha2; region and platform are to be specified in upper case, order not important, except for username and country code which have to come 1st)
[username]_[COUNTRY_CODE]_REGION-[region]_PLATFORM-[platform]

# Run with docker
1. Prepare  postgres, apply the migrations to the database
2. Modify config.yaml, db_url to container's url

