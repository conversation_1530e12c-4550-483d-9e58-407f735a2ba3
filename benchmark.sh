#!/bin/bash
# Script to benchmark the dpn-admin server

# Check if we're running in Docker
if [ -f /.dockerenv ]; then
    echo "Running in Docker container"
    DOCKER_MODE=true
else
    echo "Running on host machine"
    DOCKER_MODE=false
fi

# Function to install dependencies
install_deps() {
    if [ "$DOCKER_MODE" = true ]; then
        apt-get update
        apt-get install -y linux-tools-common linux-tools-generic linux-tools-$(uname -r) curl
        curl -L -o /usr/local/bin/flamegraph.pl https://raw.githubusercontent.com/brendangregg/FlameGraph/master/flamegraph.pl
        chmod +x /usr/local/bin/flamegraph.pl
    else
        # For macOS
        brew install flamegraph
    fi
}

# Function to run flamegraph profiling
run_flamegraph() {
    if [ "$DOCKER_MODE" = true ]; then
        # In Docker, use perf
        echo "Running flamegraph with perf in Docker"
        PID=$(pgrep admin || pgrep -f "target/release/admin")
        
        if [ -z "$PID" ]; then
            echo "Cannot find the admin process. Make sure it's running."
            exit 1
        fi
        
        echo "Profiling process $PID for 60 seconds..."
        mkdir -p /tmp/profile
        
        # Run perf record
        perf record -F 99 -p $PID -g -- sleep 60
        
        # Convert to flamegraph
        perf script | /usr/local/bin/flamegraph.pl > /tmp/profile/flamegraph.svg
        
        echo "Profiling complete. Flamegraph saved to /tmp/profile/flamegraph.svg"
        echo "You can copy this file to your host machine using:"
        echo "docker cp <container_id>:/tmp/profile/flamegraph.svg ./flamegraph.svg"
    else
        # On macOS, use cargo flamegraph
        echo "Running cargo flamegraph on macOS"
        cargo flamegraph --bin admin
        echo "Flamegraph saved to flamegraph.svg"
    fi
}

# Function to run load testing
run_load_test() {
    echo "Running load test against the server..."
    # Use wrk for HTTP load testing
    if command -v wrk &> /dev/null; then
        # Adjust these parameters based on your server's endpoints
        wrk -t4 -c100 -d30s http://0.0.0.0:8090/metrics/health
    else
        echo "wrk not found. Please install wrk for load testing."
        echo "On macOS: brew install wrk"
        echo "On Linux: apt-get install wrk"
    fi
}

# Main execution
echo "Starting benchmark process..."

# Install dependencies if needed
read -p "Install dependencies? (y/n) " install_deps_answer
if [[ $install_deps_answer == "y" ]]; then
    install_deps
fi

# Run flamegraph
read -p "Run flamegraph profiling? (y/n) " run_flamegraph_answer
if [[ $run_flamegraph_answer == "y" ]]; then
    run_flamegraph
fi

# Run load test
read -p "Run load test? (y/n) " run_load_test_answer
if [[ $run_load_test_answer == "y" ]]; then
    run_load_test
fi

echo "Benchmark process completed."
