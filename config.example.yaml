log_level: info
web_bind: 0.0.0.0:8090
accounting_service_url: http://localhost:8093
loyalty_points_service_url: http://localhost:8094
db_url: postgresql://postgres:postgres@localhost:5432/admin
jwt_secret_key: secret
mmdb_path: ./GeoLite2-City.mmdb
x_api_key: key
rmq_uri: amqp://username:password@localhost:5672
redis_uri: redis://:dpn@localhost:6379
country_info_file_path: ./countryInfo.txt
masternode_client_url: http://localhost:9095
masternode_country_code_file_path: ./config/masternode_country_code.json

#### masternode config
masternode0: masternodeip1
masternode1: masternodeip2
masternode2: masternodeip2

# wallet cli address
wallet_cli_address: ******************************************

# online peer
online_provider_stats_url: http://*********:4300


