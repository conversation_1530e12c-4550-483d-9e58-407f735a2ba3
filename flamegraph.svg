<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg version="1.1" width="1200" height="550" onload="init(evt)" viewBox="0 0 1200 550" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:fg="http://github.com/jonhoo/inferno"><!--Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples.--><!--NOTES: --><defs><linearGradient id="background" y1="0" y2="1" x1="0" x2="0"><stop stop-color="#eeeeee" offset="5%"/><stop stop-color="#eeeeb0" offset="95%"/></linearGradient></defs><style type="text/css">
text { font-family:monospace; font-size:12px }
#title { text-anchor:middle; font-size:17px; }
#matched { text-anchor:end; }
#search { text-anchor:end; opacity:0.1; cursor:pointer; }
#search:hover, #search.show { opacity:1; }
#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
#unzoom { cursor:pointer; }
#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
.hide { display:none; }
.parent { opacity:0.5; }
</style><script type="text/ecmascript"><![CDATA[
        var nametype = 'Function:';
        var fontsize = 12;
        var fontwidth = 0.59;
        var xpad = 10;
        var inverted = false;
        var searchcolor = 'rgb(230,0,230)';
        var fluiddrawing = true;
        var truncate_text_right = false;
    ]]><![CDATA["use strict";
var details, searchbtn, unzoombtn, matchedtxt, svg, searching, frames, known_font_width;
function init(evt) {
    details = document.getElementById("details").firstChild;
    searchbtn = document.getElementById("search");
    unzoombtn = document.getElementById("unzoom");
    matchedtxt = document.getElementById("matched");
    svg = document.getElementsByTagName("svg")[0];
    frames = document.getElementById("frames");
    known_font_width = get_monospace_width(frames);
    total_samples = parseInt(frames.attributes.total_samples.value);
    searching = 0;

    // Use GET parameters to restore a flamegraph's state.
    var restore_state = function() {
        var params = get_params();
        if (params.x && params.y)
            zoom(find_group(document.querySelector('[*|x="' + params.x + '"][y="' + params.y + '"]')));
        if (params.s)
            search(params.s);
    };

    if (fluiddrawing) {
        // Make width dynamic so the SVG fits its parent's width.
        svg.removeAttribute("width");
        // Edge requires us to have a viewBox that gets updated with size changes.
        var isEdge = /Edge\/\d./i.test(navigator.userAgent);
        if (!isEdge) {
            svg.removeAttribute("viewBox");
        }
        var update_for_width_change = function() {
            if (isEdge) {
                svg.attributes.viewBox.value = "0 0 " + svg.width.baseVal.value + " " + svg.height.baseVal.value;
            }

            // Keep consistent padding on left and right of frames container.
            frames.attributes.width.value = svg.width.baseVal.value - xpad * 2;

            // Text truncation needs to be adjusted for the current width.
            update_text_for_elements(frames.children);

            // Keep search elements at a fixed distance from right edge.
            var svgWidth = svg.width.baseVal.value;
            searchbtn.attributes.x.value = svgWidth - xpad;
            matchedtxt.attributes.x.value = svgWidth - xpad;
        };
        window.addEventListener('resize', function() {
            update_for_width_change();
        });
        // This needs to be done asynchronously for Safari to work.
        setTimeout(function() {
            unzoom();
            update_for_width_change();
            restore_state();
        }, 0);
    } else {
        restore_state();
    }
}
// event listeners
window.addEventListener("click", function(e) {
    var target = find_group(e.target);
    if (target) {
        if (target.nodeName == "a") {
            if (e.ctrlKey === false) return;
            e.preventDefault();
        }
        if (target.classList.contains("parent")) unzoom();
        zoom(target);

        // set parameters for zoom state
        var el = target.querySelector("rect");
        if (el && el.attributes && el.attributes.y && el.attributes["fg:x"]) {
            var params = get_params()
            params.x = el.attributes["fg:x"].value;
            params.y = el.attributes.y.value;
            history.replaceState(null, null, parse_params(params));
        }
    }
    else if (e.target.id == "unzoom") {
        unzoom();

        // remove zoom state
        var params = get_params();
        if (params.x) delete params.x;
        if (params.y) delete params.y;
        history.replaceState(null, null, parse_params(params));
    }
    else if (e.target.id == "search") search_prompt();
}, false)
// mouse-over for info
// show
window.addEventListener("mouseover", function(e) {
    var target = find_group(e.target);
    if (target) details.nodeValue = nametype + " " + g_to_text(target);
}, false)
// clear
window.addEventListener("mouseout", function(e) {
    var target = find_group(e.target);
    if (target) details.nodeValue = ' ';
}, false)
// ctrl-F for search
window.addEventListener("keydown",function (e) {
    if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
        e.preventDefault();
        search_prompt();
    }
}, false)
// functions
function get_params() {
    var params = {};
    var paramsarr = window.location.search.substr(1).split('&');
    for (var i = 0; i < paramsarr.length; ++i) {
        var tmp = paramsarr[i].split("=");
        if (!tmp[0] || !tmp[1]) continue;
        params[tmp[0]]  = decodeURIComponent(tmp[1]);
    }
    return params;
}
function parse_params(params) {
    var uri = "?";
    for (var key in params) {
        uri += key + '=' + encodeURIComponent(params[key]) + '&';
    }
    if (uri.slice(-1) == "&")
        uri = uri.substring(0, uri.length - 1);
    if (uri == '?')
        uri = window.location.href.split('?')[0];
    return uri;
}
function find_child(node, selector) {
    var children = node.querySelectorAll(selector);
    if (children.length) return children[0];
    return;
}
function find_group(node) {
    var parent = node.parentElement;
    if (!parent) return;
    if (parent.id == "frames") return node;
    return find_group(parent);
}
function orig_save(e, attr, val) {
    if (e.attributes["fg:orig_" + attr] != undefined) return;
    if (e.attributes[attr] == undefined) return;
    if (val == undefined) val = e.attributes[attr].value;
    e.setAttribute("fg:orig_" + attr, val);
}
function orig_load(e, attr) {
    if (e.attributes["fg:orig_"+attr] == undefined) return;
    e.attributes[attr].value = e.attributes["fg:orig_" + attr].value;
    e.removeAttribute("fg:orig_" + attr);
}
function g_to_text(e) {
    var text = find_child(e, "title").firstChild.nodeValue;
    return (text)
}
function g_to_func(e) {
    var func = g_to_text(e);
    // if there's any manipulation we want to do to the function
    // name before it's searched, do it here before returning.
    return (func);
}
function get_monospace_width(frames) {
    // Given the id="frames" element, return the width of text characters if
    // this is a monospace font, otherwise return 0.
    text = find_child(frames.children[0], "text");
    originalContent = text.textContent;
    text.textContent = "!";
    bangWidth = text.getComputedTextLength();
    text.textContent = "W";
    wWidth = text.getComputedTextLength();
    text.textContent = originalContent;
    if (bangWidth === wWidth) {
        return bangWidth;
    } else {
        return 0;
    }
}
function update_text_for_elements(elements) {
    // In order to render quickly in the browser, you want to do one pass of
    // reading attributes, and one pass of mutating attributes. See
    // https://web.dev/avoid-large-complex-layouts-and-layout-thrashing/ for details.

    // Fall back to inefficient calculation, if we're variable-width font.
    // TODO This should be optimized somehow too.
    if (known_font_width === 0) {
        for (var i = 0; i < elements.length; i++) {
            update_text(elements[i]);
        }
        return;
    }

    var textElemNewAttributes = [];
    for (var i = 0; i < elements.length; i++) {
        var e = elements[i];
        var r = find_child(e, "rect");
        var t = find_child(e, "text");
        var w = parseFloat(r.attributes.width.value) * frames.attributes.width.value / 100 - 3;
        var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
        var newX = format_percent((parseFloat(r.attributes.x.value) + (100 * 3 / frames.attributes.width.value)));

        // Smaller than this size won't fit anything
        if (w < 2 * known_font_width) {
            textElemNewAttributes.push([newX, ""]);
            continue;
        }

        // Fit in full text width
        if (txt.length * known_font_width < w) {
            textElemNewAttributes.push([newX, txt]);
            continue;
        }

        var substringLength = Math.floor(w / known_font_width) - 2;
        if (truncate_text_right) {
            // Truncate the right side of the text.
            textElemNewAttributes.push([newX, txt.substring(0, substringLength) + ".."]);
            continue;
        } else {
            // Truncate the left side of the text.
            textElemNewAttributes.push([newX, ".." + txt.substring(txt.length - substringLength, txt.length)]);
            continue;
        }
    }

    console.assert(textElemNewAttributes.length === elements.length, "Resize failed, please file a bug at https://github.com/jonhoo/inferno/");

    // Now that we know new textContent, set it all in one go so we don't refresh a bazillion times.
    for (var i = 0; i < elements.length; i++) {
        var e = elements[i];
        var values = textElemNewAttributes[i];
        var t = find_child(e, "text");
        t.attributes.x.value = values[0];
        t.textContent = values[1];
    }
}

function update_text(e) {
    var r = find_child(e, "rect");
    var t = find_child(e, "text");
    var w = parseFloat(r.attributes.width.value) * frames.attributes.width.value / 100 - 3;
    var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
    t.attributes.x.value = format_percent((parseFloat(r.attributes.x.value) + (100 * 3 / frames.attributes.width.value)));

    // Smaller than this size won't fit anything
    if (w < 2 * fontsize * fontwidth) {
        t.textContent = "";
        return;
    }
    t.textContent = txt;
    // Fit in full text width
    if (t.getComputedTextLength() < w)
        return;
    if (truncate_text_right) {
        // Truncate the right side of the text.
        for (var x = txt.length - 2; x > 0; x--) {
            if (t.getSubStringLength(0, x + 2) <= w) {
                t.textContent = txt.substring(0, x) + "..";
                return;
            }
        }
    } else {
        // Truncate the left side of the text.
        for (var x = 2; x < txt.length; x++) {
            if (t.getSubStringLength(x - 2, txt.length) <= w) {
                t.textContent = ".." + txt.substring(x, txt.length);
                return;
            }
        }
    }
    t.textContent = "";
}
// zoom
function zoom_reset(e) {
    if (e.tagName == "rect") {
        e.attributes.x.value = format_percent(100 * parseInt(e.attributes["fg:x"].value) / total_samples);
        e.attributes.width.value = format_percent(100 * parseInt(e.attributes["fg:w"].value) / total_samples);
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_reset(c[i]);
    }
}
function zoom_child(e, x, zoomed_width_samples) {
    if (e.tagName == "text") {
        var parent_x = parseFloat(find_child(e.parentNode, "rect[x]").attributes.x.value);
        e.attributes.x.value = format_percent(parent_x + (100 * 3 / frames.attributes.width.value));
    } else if (e.tagName == "rect") {
        e.attributes.x.value = format_percent(100 * (parseInt(e.attributes["fg:x"].value) - x) / zoomed_width_samples);
        e.attributes.width.value = format_percent(100 * parseInt(e.attributes["fg:w"].value) / zoomed_width_samples);
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_child(c[i], x, zoomed_width_samples);
    }
}
function zoom_parent(e) {
    if (e.attributes) {
        if (e.attributes.x != undefined) {
            e.attributes.x.value = "0.0%";
        }
        if (e.attributes.width != undefined) {
            e.attributes.width.value = "100.0%";
        }
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_parent(c[i]);
    }
}
function zoom(node) {
    var attr = find_child(node, "rect").attributes;
    var width = parseInt(attr["fg:w"].value);
    var xmin = parseInt(attr["fg:x"].value);
    var xmax = xmin + width;
    var ymin = parseFloat(attr.y.value);
    unzoombtn.classList.remove("hide");
    var el = frames.children;
    var to_update_text = [];
    for (var i = 0; i < el.length; i++) {
        var e = el[i];
        var a = find_child(e, "rect").attributes;
        var ex = parseInt(a["fg:x"].value);
        var ew = parseInt(a["fg:w"].value);
        // Is it an ancestor
        if (!inverted) {
            var upstack = parseFloat(a.y.value) > ymin;
        } else {
            var upstack = parseFloat(a.y.value) < ymin;
        }
        if (upstack) {
            // Direct ancestor
            if (ex <= xmin && (ex+ew) >= xmax) {
                e.classList.add("parent");
                zoom_parent(e);
                to_update_text.push(e);
            }
            // not in current path
            else
                e.classList.add("hide");
        }
        // Children maybe
        else {
            // no common path
            if (ex < xmin || ex >= xmax) {
                e.classList.add("hide");
            }
            else {
                zoom_child(e, xmin, width);
                to_update_text.push(e);
            }
        }
    }
    update_text_for_elements(to_update_text);
}
function unzoom() {
    unzoombtn.classList.add("hide");
    var el = frames.children;
    for(var i = 0; i < el.length; i++) {
        el[i].classList.remove("parent");
        el[i].classList.remove("hide");
        zoom_reset(el[i]);
    }
    update_text_for_elements(el);
}
// search
function reset_search() {
    var el = document.querySelectorAll("#frames rect");
    for (var i = 0; i < el.length; i++) {
        orig_load(el[i], "fill")
    }
    var params = get_params();
    delete params.s;
    history.replaceState(null, null, parse_params(params));
}
function search_prompt() {
    if (!searching) {
        var term = prompt("Enter a search term (regexp " +
            "allowed, eg: ^ext4_)", "");
        if (term != null) {
            search(term)
        }
    } else {
        reset_search();
        searching = 0;
        searchbtn.classList.remove("show");
        searchbtn.firstChild.nodeValue = "Search"
        matchedtxt.classList.add("hide");
        matchedtxt.firstChild.nodeValue = ""
    }
}
function search(term) {
    var re = new RegExp(term);
    var el = frames.children;
    var matches = new Object();
    var maxwidth = 0;
    for (var i = 0; i < el.length; i++) {
        var e = el[i];
        // Skip over frames which are either not visible, or below the zoomed-to frame
        if (e.classList.contains("hide") || e.classList.contains("parent")) {
            continue;
        }
        var func = g_to_func(e);
        var rect = find_child(e, "rect");
        if (func == null || rect == null)
            continue;
        // Save max width. Only works as we have a root frame
        var w = parseInt(rect.attributes["fg:w"].value);
        if (w > maxwidth)
            maxwidth = w;
        if (func.match(re)) {
            // highlight
            var x = parseInt(rect.attributes["fg:x"].value);
            orig_save(rect, "fill");
            rect.attributes.fill.value = searchcolor;
            // remember matches
            if (matches[x] == undefined) {
                matches[x] = w;
            } else {
                if (w > matches[x]) {
                    // overwrite with parent
                    matches[x] = w;
                }
            }
            searching = 1;
        }
    }
    if (!searching)
        return;
    var params = get_params();
    params.s = term;
    history.replaceState(null, null, parse_params(params));

    searchbtn.classList.add("show");
    searchbtn.firstChild.nodeValue = "Reset Search";
    // calculate percent matched, excluding vertical overlap
    var count = 0;
    var lastx = -1;
    var lastw = 0;
    var keys = Array();
    for (k in matches) {
        if (matches.hasOwnProperty(k))
            keys.push(k);
    }
    // sort the matched frames by their x location
    // ascending, then width descending
    keys.sort(function(a, b){
        return a - b;
    });
    // Step through frames saving only the biggest bottom-up frames
    // thanks to the sort order. This relies on the tree property
    // where children are always smaller than their parents.
    for (var k in keys) {
        var x = parseInt(keys[k]);
        var w = matches[keys[k]];
        if (x >= lastx + lastw) {
            count += w;
            lastx = x;
            lastw = w;
        }
    }
    // display matched percent
    matchedtxt.classList.remove("hide");
    var pct = 100 * count / maxwidth;
    if (pct != 100) pct = pct.toFixed(1);
    matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
}
function format_percent(n) {
    return n.toFixed(4) + "%";
}
]]></script><rect x="0" y="0" width="100%" height="550" fill="url(#background)"/><text id="title" fill="rgb(0,0,0)" x="50.0000%" y="24.00">Flame Graph</text><text id="details" fill="rgb(0,0,0)" x="10" y="533.00"> </text><text id="unzoom" class="hide" fill="rgb(0,0,0)" x="10" y="24.00">Reset Zoom</text><text id="search" fill="rgb(0,0,0)" x="1190" y="24.00">Search</text><text id="matched" fill="rgb(0,0,0)" x="1190" y="533.00"> </text><svg id="frames" x="10" width="1180" total_samples="20"><g><title>dyld4::APIs::runAllInitializersForMain() (1 samples, 5.00%)</title><rect x="0.0000%" y="437" width="5.0000%" height="15" fill="rgb(227,0,7)" fg:x="0" fg:w="1"/><text x="0.2500%" y="447.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUpPlusUpwardLinks(dyld4::RuntimeState&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="421" width="5.0000%" height="15" fill="rgb(217,0,24)" fg:x="0" fg:w="1"/><text x="0.2500%" y="431.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUpPlusUpwardLinks(dyld4::RuntimeState&amp;) const::$_0::operator()() const (1 samples, 5.00%)</title><rect x="0.0000%" y="405" width="5.0000%" height="15" fill="rgb(221,193,54)" fg:x="0" fg:w="1"/><text x="0.2500%" y="415.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUp(dyld4::RuntimeState&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="389" width="5.0000%" height="15" fill="rgb(248,212,6)" fg:x="0" fg:w="1"/><text x="0.2500%" y="399.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUp(dyld4::RuntimeState&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="373" width="5.0000%" height="15" fill="rgb(208,68,35)" fg:x="0" fg:w="1"/><text x="0.2500%" y="383.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUp(dyld4::RuntimeState&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="357" width="5.0000%" height="15" fill="rgb(232,128,0)" fg:x="0" fg:w="1"/><text x="0.2500%" y="367.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUp(dyld4::RuntimeState&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="341" width="5.0000%" height="15" fill="rgb(207,160,47)" fg:x="0" fg:w="1"/><text x="0.2500%" y="351.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUp(dyld4::RuntimeState&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="325" width="5.0000%" height="15" fill="rgb(228,23,34)" fg:x="0" fg:w="1"/><text x="0.2500%" y="335.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUp(dyld4::RuntimeState&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="309" width="5.0000%" height="15" fill="rgb(218,30,26)" fg:x="0" fg:w="1"/><text x="0.2500%" y="319.50">dyld4:..</text></g><g><title>dyld4::Loader::runInitializersBottomUp(dyld4::RuntimeState&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;, dyld3::Array&lt;dyld4::Loader const*&gt;&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="293" width="5.0000%" height="15" fill="rgb(220,122,19)" fg:x="0" fg:w="1"/><text x="0.2500%" y="303.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::runInitializers(dyld4::RuntimeState&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="277" width="5.0000%" height="15" fill="rgb(250,228,42)" fg:x="0" fg:w="1"/><text x="0.2500%" y="287.50">dyld4:..</text></g><g><title>dyld4::Loader::findAndRunAllInitializers(dyld4::RuntimeState&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="261" width="5.0000%" height="15" fill="rgb(240,193,28)" fg:x="0" fg:w="1"/><text x="0.2500%" y="271.50">dyld4:..</text></g><g><title>dyld3::MachOAnalyzer::forEachInitializer(Diagnostics&amp;, dyld3::MachOAnalyzer::VMAddrConverter const&amp;, void (unsigned int) block_pointer, void const*) const (1 samples, 5.00%)</title><rect x="0.0000%" y="245" width="5.0000%" height="15" fill="rgb(216,20,37)" fg:x="0" fg:w="1"/><text x="0.2500%" y="255.50">dyld3:..</text></g><g><title>mach_o::Header::forEachSection(void (mach_o::Header::SectionInfo const&amp;, bool&amp;) block_pointer) const (1 samples, 5.00%)</title><rect x="0.0000%" y="229" width="5.0000%" height="15" fill="rgb(206,188,39)" fg:x="0" fg:w="1"/><text x="0.2500%" y="239.50">mach_o..</text></g><g><title>mach_o::Header::forEachLoadCommand(void (load_command const*, bool&amp;) block_pointer) const (1 samples, 5.00%)</title><rect x="0.0000%" y="213" width="5.0000%" height="15" fill="rgb(217,207,13)" fg:x="0" fg:w="1"/><text x="0.2500%" y="223.50">mach_o..</text></g><g><title>invocation function for block in mach_o::Header::forEachSection(void (mach_o::Header::SectionInfo const&amp;, bool&amp;) block_pointer) const (1 samples, 5.00%)</title><rect x="0.0000%" y="197" width="5.0000%" height="15" fill="rgb(231,73,38)" fg:x="0" fg:w="1"/><text x="0.2500%" y="207.50">invoca..</text></g><g><title>invocation function for block in dyld3::MachOAnalyzer::forEachInitializer(Diagnostics&amp;, dyld3::MachOAnalyzer::VMAddrConverter const&amp;, void (unsigned int) block_pointer, void const*) const (1 samples, 5.00%)</title><rect x="0.0000%" y="181" width="5.0000%" height="15" fill="rgb(225,20,46)" fg:x="0" fg:w="1"/><text x="0.2500%" y="191.50">invoca..</text></g><g><title>invocation function for block in dyld4::Loader::findAndRunAllInitializers(dyld4::RuntimeState&amp;) const (1 samples, 5.00%)</title><rect x="0.0000%" y="165" width="5.0000%" height="15" fill="rgb(210,31,41)" fg:x="0" fg:w="1"/><text x="0.2500%" y="175.50">invoca..</text></g><g><title>csops (1 samples, 5.00%)</title><rect x="0.0000%" y="149" width="5.0000%" height="15" fill="rgb(221,200,47)" fg:x="0" fg:w="1"/><text x="0.2500%" y="159.50">csops</text></g><g><title>dyld4::JustInTimeLoader::applyFixups(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::DyldCacheDataConstLazyScopedWriter&amp;, bool, lsl::Vector&lt;std::__1::pair&lt;dyld4::Loader const*, char const*&gt;&gt;*) const (1 samples, 5.00%)</title><rect x="5.0000%" y="437" width="5.0000%" height="15" fill="rgb(226,26,5)" fg:x="1" fg:w="1"/><text x="5.2500%" y="447.50">dyld4:..</text></g><g><title>dyld4::Loader::applyFixupsGeneric(Diagnostics&amp;, dyld4::RuntimeState&amp;, unsigned long long, dyld3::Array&lt;void const*&gt; const&amp;, dyld3::Array&lt;void const*&gt; const&amp;, bool, dyld3::Array&lt;dyld4::Loader::MissingFlatLazySymbol&gt; const&amp;) const (1 samples, 5.00%)</title><rect x="5.0000%" y="421" width="5.0000%" height="15" fill="rgb(249,33,26)" fg:x="1" fg:w="1"/><text x="5.2500%" y="431.50">dyld4:..</text></g><g><title>dyld3::MachOAnalyzer::forEachRebaseLocation_Opcodes(Diagnostics&amp;, void (unsigned long long, bool&amp;) block_pointer) const (1 samples, 5.00%)</title><rect x="5.0000%" y="405" width="5.0000%" height="15" fill="rgb(235,183,28)" fg:x="1" fg:w="1"/><text x="5.2500%" y="415.50">dyld3:..</text></g><g><title>dyld3::MachOAnalyzer::forEachRebase_Opcodes(Diagnostics&amp;, dyld3::MachOLoaded::LinkEditInfo const&amp;, mach_o::Header::SegmentInfo const*, void (char const*, dyld3::MachOLoaded::LinkEditInfo const&amp;, mach_o::Header::SegmentInfo const*, bool, unsigned int, unsigned char, unsigned long long, dyld3::MachOAnalyzer::Rebase, bool&amp;) block_pointer) const (1 samples, 5.00%)</title><rect x="5.0000%" y="389" width="5.0000%" height="15" fill="rgb(221,5,38)" fg:x="1" fg:w="1"/><text x="5.2500%" y="399.50">dyld3:..</text></g><g><title>invocation function for block in dyld4::Loader::applyFixupsGeneric(Diagnostics&amp;, dyld4::RuntimeState&amp;, unsigned long long, dyld3::Array&lt;void const*&gt; const&amp;, dyld3::Array&lt;void const*&gt; const&amp;, bool, dyld3::Array&lt;dyld4::Loader::MissingFlatLazySymbol&gt; const&amp;) const (1 samples, 5.00%)</title><rect x="5.0000%" y="373" width="5.0000%" height="15" fill="rgb(247,18,42)" fg:x="1" fg:w="1"/><text x="5.2500%" y="383.50">invoca..</text></g><g><title>dyld4::JustInTimeLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="437" width="5.0000%" height="15" fill="rgb(241,131,45)" fg:x="2" fg:w="1"/><text x="10.2500%" y="447.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="421" width="5.0000%" height="15" fill="rgb(249,31,29)" fg:x="2" fg:w="1"/><text x="10.2500%" y="431.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="405" width="5.0000%" height="15" fill="rgb(225,111,53)" fg:x="2" fg:w="1"/><text x="10.2500%" y="415.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="389" width="5.0000%" height="15" fill="rgb(238,160,17)" fg:x="2" fg:w="1"/><text x="10.2500%" y="399.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="373" width="5.0000%" height="15" fill="rgb(214,148,48)" fg:x="2" fg:w="1"/><text x="10.2500%" y="383.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="357" width="5.0000%" height="15" fill="rgb(232,36,49)" fg:x="2" fg:w="1"/><text x="10.2500%" y="367.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="341" width="5.0000%" height="15" fill="rgb(209,103,24)" fg:x="2" fg:w="1"/><text x="10.2500%" y="351.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="325" width="5.0000%" height="15" fill="rgb(229,88,8)" fg:x="2" fg:w="1"/><text x="10.2500%" y="335.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="309" width="5.0000%" height="15" fill="rgb(213,181,19)" fg:x="2" fg:w="1"/><text x="10.2500%" y="319.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="293" width="5.0000%" height="15" fill="rgb(254,191,54)" fg:x="2" fg:w="1"/><text x="10.2500%" y="303.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="277" width="5.0000%" height="15" fill="rgb(241,83,37)" fg:x="2" fg:w="1"/><text x="10.2500%" y="287.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="261" width="5.0000%" height="15" fill="rgb(233,36,39)" fg:x="2" fg:w="1"/><text x="10.2500%" y="271.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="245" width="5.0000%" height="15" fill="rgb(226,3,54)" fg:x="2" fg:w="1"/><text x="10.2500%" y="255.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="229" width="5.0000%" height="15" fill="rgb(245,192,40)" fg:x="2" fg:w="1"/><text x="10.2500%" y="239.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="213" width="5.0000%" height="15" fill="rgb(238,167,29)" fg:x="2" fg:w="1"/><text x="10.2500%" y="223.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="197" width="5.0000%" height="15" fill="rgb(232,182,51)" fg:x="2" fg:w="1"/><text x="10.2500%" y="207.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="181" width="5.0000%" height="15" fill="rgb(231,60,39)" fg:x="2" fg:w="1"/><text x="10.2500%" y="191.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="165" width="5.0000%" height="15" fill="rgb(208,69,12)" fg:x="2" fg:w="1"/><text x="10.2500%" y="175.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="149" width="5.0000%" height="15" fill="rgb(235,93,37)" fg:x="2" fg:w="1"/><text x="10.2500%" y="159.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="133" width="5.0000%" height="15" fill="rgb(213,116,39)" fg:x="2" fg:w="1"/><text x="10.2500%" y="143.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="117" width="5.0000%" height="15" fill="rgb(222,207,29)" fg:x="2" fg:w="1"/><text x="10.2500%" y="127.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="101" width="5.0000%" height="15" fill="rgb(206,96,30)" fg:x="2" fg:w="1"/><text x="10.2500%" y="111.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="85" width="5.0000%" height="15" fill="rgb(218,138,4)" fg:x="2" fg:w="1"/><text x="10.2500%" y="95.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="69" width="5.0000%" height="15" fill="rgb(250,191,14)" fg:x="2" fg:w="1"/><text x="10.2500%" y="79.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="53" width="5.0000%" height="15" fill="rgb(239,60,40)" fg:x="2" fg:w="1"/><text x="10.2500%" y="63.50">dyld4:..</text></g><g><title>dyld4::PrebuiltLoader::loadDependents(Diagnostics&amp;, dyld4::RuntimeState&amp;, dyld4::Loader::LoadOptions const&amp;) (1 samples, 5.00%)</title><rect x="10.0000%" y="37" width="5.0000%" height="15" fill="rgb(206,27,48)" fg:x="2" fg:w="1"/><text x="10.2500%" y="47.50">dyld4:..</text></g><g><title>dyld4::start(dyld4::KernelArgs*, void*, void*)::$_0::operator()() const (4 samples, 20.00%)</title><rect x="0.0000%" y="469" width="20.0000%" height="15" fill="rgb(225,35,8)" fg:x="0" fg:w="4"/><text x="0.2500%" y="479.50">dyld4::start(dyld4::KernelArgs*..</text></g><g><title>dyld4::prepare(dyld4::APIs&amp;, mach_o::Header const*) (4 samples, 20.00%)</title><rect x="0.0000%" y="453" width="20.0000%" height="15" fill="rgb(250,213,24)" fg:x="0" fg:w="4"/><text x="0.2500%" y="463.50">dyld4::prepare(dyld4::APIs&amp;, ma..</text></g><g><title>dyld4::RuntimeState::notifyDtrace(std::__1::span&lt;dyld4::Loader const*, 18446744073709551615ul&gt; const&amp;) (1 samples, 5.00%)</title><rect x="15.0000%" y="437" width="5.0000%" height="15" fill="rgb(247,123,22)" fg:x="3" fg:w="1"/><text x="15.2500%" y="447.50">dyld4:..</text></g><g><title>dyld4::SyscallDelegate::dtraceRegisterUserProbes(dof_ioctl_data*) const (1 samples, 5.00%)</title><rect x="15.0000%" y="421" width="5.0000%" height="15" fill="rgb(231,138,38)" fg:x="3" fg:w="1"/><text x="15.2500%" y="431.50">dyld4:..</text></g><g><title>__open (1 samples, 5.00%)</title><rect x="15.0000%" y="405" width="5.0000%" height="15" fill="rgb(231,145,46)" fg:x="3" fg:w="1"/><text x="15.2500%" y="415.50">__open</text></g><g><title>&lt;&amp;str as lapin::connection::Connect&gt;::connect::_{{closure}} (1 samples, 5.00%)</title><rect x="20.0000%" y="245" width="5.0000%" height="15" fill="rgb(251,118,11)" fg:x="4" fg:w="1"/><text x="20.2500%" y="255.50">&lt;&amp;str ..</text></g><g><title>&lt;amq_protocol_uri::AMQPUri as lapin::connection::Connect&gt;::connect::_{{closure}} (1 samples, 5.00%)</title><rect x="20.0000%" y="229" width="5.0000%" height="15" fill="rgb(217,147,25)" fg:x="4" fg:w="1"/><text x="20.2500%" y="239.50">&lt;amq_p..</text></g><g><title>&lt;pinky_swear::PinkySwear&lt;T&gt; as core::future::future::Future&gt;::poll (1 samples, 5.00%)</title><rect x="20.0000%" y="213" width="5.0000%" height="15" fill="rgb(247,81,37)" fg:x="4" fg:w="1"/><text x="20.2500%" y="223.50">&lt;pinky..</text></g><g><title>&lt;core::future::poll_fn::PollFn&lt;F&gt; as core::future::future::Future&gt;::poll (1 samples, 5.00%)</title><rect x="25.0000%" y="245" width="5.0000%" height="15" fill="rgb(209,12,38)" fg:x="5" fg:w="1"/><text x="25.2500%" y="255.50">&lt;core:..</text></g><g><title>&lt;tokio::future::maybe_done::MaybeDone&lt;Fut&gt; as core::future::future::Future&gt;::poll (1 samples, 5.00%)</title><rect x="25.0000%" y="229" width="5.0000%" height="15" fill="rgb(227,1,9)" fg:x="5" fg:w="1"/><text x="25.2500%" y="239.50">&lt;tokio..</text></g><g><title>dpn_core::services::redis::RedisService::del (1 samples, 5.00%)</title><rect x="25.0000%" y="213" width="5.0000%" height="15" fill="rgb(248,47,43)" fg:x="5" fg:w="1"/><text x="25.2500%" y="223.50">dpn_co..</text></g><g><title>close (1 samples, 5.00%)</title><rect x="25.0000%" y="197" width="5.0000%" height="15" fill="rgb(221,10,30)" fg:x="5" fg:w="1"/><text x="25.2500%" y="207.50">close</text></g><g><title>dpn_admin::utils::iso_to_geoname_id::IsoToGeonameIdService::new (1 samples, 5.00%)</title><rect x="30.0000%" y="245" width="5.0000%" height="15" fill="rgb(210,229,1)" fg:x="6" fg:w="1"/><text x="30.2500%" y="255.50">dpn_ad..</text></g><g><title>hashbrown::map::HashMap&lt;K,V,S,A&gt;::insert (1 samples, 5.00%)</title><rect x="30.0000%" y="229" width="5.0000%" height="15" fill="rgb(222,148,37)" fg:x="6" fg:w="1"/><text x="30.2500%" y="239.50">hashbr..</text></g><g><title>hashbrown::raw::RawTable&lt;T,A&gt;::reserve_rehash (1 samples, 5.00%)</title><rect x="30.0000%" y="213" width="5.0000%" height="15" fill="rgb(234,67,33)" fg:x="6" fg:w="1"/><text x="30.2500%" y="223.50">hashbr..</text></g><g><title>__connect (1 samples, 5.00%)</title><rect x="35.0000%" y="165" width="5.0000%" height="15" fill="rgb(247,98,35)" fg:x="7" fg:w="1"/><text x="35.2500%" y="175.50">__conn..</text></g><g><title>start (13 samples, 65.00%)</title><rect x="0.0000%" y="485" width="65.0000%" height="15" fill="rgb(247,138,52)" fg:x="0" fg:w="13"/><text x="0.2500%" y="495.50">start</text></g><g><title>main (9 samples, 45.00%)</title><rect x="20.0000%" y="469" width="45.0000%" height="15" fill="rgb(213,79,30)" fg:x="4" fg:w="9"/><text x="20.2500%" y="479.50">main</text></g><g><title>std::rt::lang_start_internal (9 samples, 45.00%)</title><rect x="20.0000%" y="453" width="45.0000%" height="15" fill="rgb(246,177,23)" fg:x="4" fg:w="9"/><text x="20.2500%" y="463.50">std::rt::lang_start_internal</text></g><g><title>std::rt::lang_start::_{{closure}} (9 samples, 45.00%)</title><rect x="20.0000%" y="437" width="45.0000%" height="15" fill="rgb(230,62,27)" fg:x="4" fg:w="9"/><text x="20.2500%" y="447.50">std::rt::lang_start::_{{closure}}</text></g><g><title>std::sys::backtrace::__rust_begin_short_backtrace (9 samples, 45.00%)</title><rect x="20.0000%" y="421" width="45.0000%" height="15" fill="rgb(216,154,8)" fg:x="4" fg:w="9"/><text x="20.2500%" y="431.50">std::sys::backtrace::__rust_begin_short_backtrace</text></g><g><title>admin::main (9 samples, 45.00%)</title><rect x="20.0000%" y="405" width="45.0000%" height="15" fill="rgb(244,35,45)" fg:x="4" fg:w="9"/><text x="20.2500%" y="415.50">admin::main</text></g><g><title>tokio::runtime::runtime::Runtime::block_on (9 samples, 45.00%)</title><rect x="20.0000%" y="389" width="45.0000%" height="15" fill="rgb(251,115,12)" fg:x="4" fg:w="9"/><text x="20.2500%" y="399.50">tokio::runtime::runtime::Runtime::block_on</text></g><g><title>tokio::runtime::scheduler::current_thread::CurrentThread::block_on (9 samples, 45.00%)</title><rect x="20.0000%" y="373" width="45.0000%" height="15" fill="rgb(240,54,50)" fg:x="4" fg:w="9"/><text x="20.2500%" y="383.50">tokio::runtime::scheduler::current_thread::CurrentThread::block_on</text></g><g><title>tokio::runtime::context::runtime::enter_runtime (9 samples, 45.00%)</title><rect x="20.0000%" y="357" width="45.0000%" height="15" fill="rgb(233,84,52)" fg:x="4" fg:w="9"/><text x="20.2500%" y="367.50">tokio::runtime::context::runtime::enter_runtime</text></g><g><title>tokio::runtime::scheduler::current_thread::CoreGuard::block_on (9 samples, 45.00%)</title><rect x="20.0000%" y="341" width="45.0000%" height="15" fill="rgb(207,117,47)" fg:x="4" fg:w="9"/><text x="20.2500%" y="351.50">tokio::runtime::scheduler::current_thread::CoreGuard::block_on</text></g><g><title>tokio::runtime::context::scoped::Scoped&lt;T&gt;::set (9 samples, 45.00%)</title><rect x="20.0000%" y="325" width="45.0000%" height="15" fill="rgb(249,43,39)" fg:x="4" fg:w="9"/><text x="20.2500%" y="335.50">tokio::runtime::context::scoped::Scoped&lt;T&gt;::set</text></g><g><title>tokio::runtime::scheduler::current_thread::Context::enter (9 samples, 45.00%)</title><rect x="20.0000%" y="309" width="45.0000%" height="15" fill="rgb(209,38,44)" fg:x="4" fg:w="9"/><text x="20.2500%" y="319.50">tokio::runtime::scheduler::current_thread::Context::enter</text></g><g><title>&lt;core::pin::Pin&lt;P&gt; as core::future::future::Future&gt;::poll (9 samples, 45.00%)</title><rect x="20.0000%" y="293" width="45.0000%" height="15" fill="rgb(236,212,23)" fg:x="4" fg:w="9"/><text x="20.2500%" y="303.50">&lt;core::pin::Pin&lt;P&gt; as core::future::future::Future&gt;::poll</text></g><g><title>std::thread::local::LocalKey&lt;T&gt;::with (9 samples, 45.00%)</title><rect x="20.0000%" y="277" width="45.0000%" height="15" fill="rgb(242,79,21)" fg:x="4" fg:w="9"/><text x="20.2500%" y="287.50">std::thread::local::LocalKey&lt;T&gt;::with</text></g><g><title>admin::main::_{{closure}} (9 samples, 45.00%)</title><rect x="20.0000%" y="261" width="45.0000%" height="15" fill="rgb(211,96,35)" fg:x="4" fg:w="9"/><text x="20.2500%" y="271.50">admin::main::_{{closure}}</text></g><g><title>dpn_db::connection::ConnectionPoolBuilder::build::_{{closure}} (6 samples, 30.00%)</title><rect x="35.0000%" y="245" width="30.0000%" height="15" fill="rgb(253,215,40)" fg:x="7" fg:w="6"/><text x="35.2500%" y="255.50">dpn_db::connection::ConnectionPoolBuilder::build..</text></g><g><title>&lt;tokio::time::timeout::Timeout&lt;T&gt; as core::future::future::Future&gt;::poll (6 samples, 30.00%)</title><rect x="35.0000%" y="229" width="30.0000%" height="15" fill="rgb(211,81,21)" fg:x="7" fg:w="6"/><text x="35.2500%" y="239.50">&lt;tokio::time::timeout::Timeout&lt;T&gt; as core::futur..</text></g><g><title>sqlx_core::pool::inner::PoolInner&lt;DB&gt;::acquire::_{{closure}}::_{{closure}} (6 samples, 30.00%)</title><rect x="35.0000%" y="213" width="30.0000%" height="15" fill="rgb(208,190,38)" fg:x="7" fg:w="6"/><text x="35.2500%" y="223.50">sqlx_core::pool::inner::PoolInner&lt;DB&gt;::acquire::..</text></g><g><title>&lt;tokio::time::timeout::Timeout&lt;T&gt; as core::future::future::Future&gt;::poll (6 samples, 30.00%)</title><rect x="35.0000%" y="197" width="30.0000%" height="15" fill="rgb(235,213,38)" fg:x="7" fg:w="6"/><text x="35.2500%" y="207.50">&lt;tokio::time::timeout::Timeout&lt;T&gt; as core::futur..</text></g><g><title>sqlx_postgres::connection::establish::_&lt;impl sqlx_postgres::connection::PgConnection&gt;::establish::_{{closure}} (6 samples, 30.00%)</title><rect x="35.0000%" y="181" width="30.0000%" height="15" fill="rgb(237,122,38)" fg:x="7" fg:w="6"/><text x="35.2500%" y="191.50">sqlx_postgres::connection::establish::_&lt;impl sql..</text></g><g><title>sha2::sha256::compress256 (5 samples, 25.00%)</title><rect x="40.0000%" y="165" width="25.0000%" height="15" fill="rgb(244,218,35)" fg:x="8" fg:w="5"/><text x="40.2500%" y="175.50">sha2::sha256::compress256</text></g><g><title>&lt;futures_lite::future::Or&lt;F1,F2&gt; as core::future::future::Future&gt;::poll (2 samples, 10.00%)</title><rect x="65.0000%" y="357" width="10.0000%" height="15" fill="rgb(240,68,47)" fg:x="13" fg:w="2"/><text x="65.2500%" y="367.50">&lt;futures_lite:..</text></g><g><title>&lt;async_channel::RecvInner&lt;T&gt; as event_listener_strategy::EventListenerFuture&gt;::poll_with_strategy (1 samples, 5.00%)</title><rect x="70.0000%" y="341" width="5.0000%" height="15" fill="rgb(210,16,53)" fg:x="14" fg:w="1"/><text x="70.2500%" y="351.50">&lt;async..</text></g><g><title>event_listener::Event&lt;T&gt;::listen (1 samples, 5.00%)</title><rect x="70.0000%" y="325" width="5.0000%" height="15" fill="rgb(235,124,12)" fg:x="14" fg:w="1"/><text x="70.2500%" y="335.50">event_..</text></g><g><title>event_listener::Event&lt;T&gt;::inner (1 samples, 5.00%)</title><rect x="70.0000%" y="309" width="5.0000%" height="15" fill="rgb(224,169,11)" fg:x="14" fg:w="1"/><text x="70.2500%" y="319.50">event_..</text></g><g><title>szone_malloc_should_clear (1 samples, 5.00%)</title><rect x="70.0000%" y="293" width="5.0000%" height="15" fill="rgb(250,166,2)" fg:x="14" fg:w="1"/><text x="70.2500%" y="303.50">szone_..</text></g><g><title>tiny_malloc_should_clear (1 samples, 5.00%)</title><rect x="70.0000%" y="277" width="5.0000%" height="15" fill="rgb(242,216,29)" fg:x="14" fg:w="1"/><text x="70.2500%" y="287.50">tiny_m..</text></g><g><title>mvm_allocate_pages_plat (1 samples, 5.00%)</title><rect x="70.0000%" y="261" width="5.0000%" height="15" fill="rgb(230,116,27)" fg:x="14" fg:w="1"/><text x="70.2500%" y="271.50">mvm_al..</text></g><g><title>_kernelrpc_mach_vm_map_trap (1 samples, 5.00%)</title><rect x="70.0000%" y="245" width="5.0000%" height="15" fill="rgb(228,99,48)" fg:x="14" fg:w="1"/><text x="70.2500%" y="255.50">_kerne..</text></g><g><title>async_global_executor::threading::thread_main_loop (3 samples, 15.00%)</title><rect x="65.0000%" y="405" width="15.0000%" height="15" fill="rgb(253,11,6)" fg:x="13" fg:w="3"/><text x="65.2500%" y="415.50">async_global_executor::..</text></g><g><title>async_io::driver::block_on (3 samples, 15.00%)</title><rect x="65.0000%" y="389" width="15.0000%" height="15" fill="rgb(247,143,39)" fg:x="13" fg:w="3"/><text x="65.2500%" y="399.50">async_io::driver::block..</text></g><g><title>std::thread::local::LocalKey&lt;T&gt;::with (3 samples, 15.00%)</title><rect x="65.0000%" y="373" width="15.0000%" height="15" fill="rgb(236,97,10)" fg:x="13" fg:w="3"/><text x="65.2500%" y="383.50">std::thread::local::Loc..</text></g><g><title>parking::Inner::park (1 samples, 5.00%)</title><rect x="75.0000%" y="357" width="5.0000%" height="15" fill="rgb(233,208,19)" fg:x="15" fg:w="1"/><text x="75.2500%" y="367.50">parkin..</text></g><g><title>__psynch_cvwait (1 samples, 5.00%)</title><rect x="75.0000%" y="341" width="5.0000%" height="15" fill="rgb(216,164,2)" fg:x="15" fg:w="1"/><text x="75.2500%" y="351.50">__psyn..</text></g><g><title>blocking::Executor::main_loop (1 samples, 5.00%)</title><rect x="80.0000%" y="405" width="5.0000%" height="15" fill="rgb(220,129,5)" fg:x="16" fg:w="1"/><text x="80.2500%" y="415.50">blocki..</text></g><g><title>async_task::raw::RawTask&lt;F,T,S,M&gt;::run (1 samples, 5.00%)</title><rect x="80.0000%" y="389" width="5.0000%" height="15" fill="rgb(242,17,10)" fg:x="16" fg:w="1"/><text x="80.2500%" y="399.50">async_..</text></g><g><title>core::ops::function::FnOnce::call_once{{vtable.shim}} (1 samples, 5.00%)</title><rect x="80.0000%" y="373" width="5.0000%" height="15" fill="rgb(242,107,0)" fg:x="16" fg:w="1"/><text x="80.2500%" y="383.50">core::..</text></g><g><title>core::ops::function::FnOnce::call_once{{vtable.shim}} (1 samples, 5.00%)</title><rect x="80.0000%" y="357" width="5.0000%" height="15" fill="rgb(251,28,31)" fg:x="16" fg:w="1"/><text x="80.2500%" y="367.50">core::..</text></g><g><title>&lt;amq_protocol_uri::AMQPUri as amq_protocol_tcp::AMQPUriTcpExt&gt;::connect_with_config (1 samples, 5.00%)</title><rect x="80.0000%" y="341" width="5.0000%" height="15" fill="rgb(233,223,10)" fg:x="16" fg:w="1"/><text x="80.2500%" y="351.50">&lt;amq_p..</text></g><g><title>tcp_stream::connect_std (1 samples, 5.00%)</title><rect x="80.0000%" y="325" width="5.0000%" height="15" fill="rgb(215,21,27)" fg:x="16" fg:w="1"/><text x="80.2500%" y="335.50">tcp_st..</text></g><g><title>&lt;str as std::net::socket_addr::ToSocketAddrs&gt;::to_socket_addrs (1 samples, 5.00%)</title><rect x="80.0000%" y="309" width="5.0000%" height="15" fill="rgb(232,23,21)" fg:x="16" fg:w="1"/><text x="80.2500%" y="319.50">&lt;str a..</text></g><g><title>&lt;std::sys::net::connection::socket::LookupHost as core::convert::TryFrom&lt;&amp;str&gt;&gt;::try_from (1 samples, 5.00%)</title><rect x="80.0000%" y="293" width="5.0000%" height="15" fill="rgb(244,5,23)" fg:x="16" fg:w="1"/><text x="80.2500%" y="303.50">&lt;std::..</text></g><g><title>&lt;std::sys::net::connection::socket::LookupHost as core::convert::TryFrom&lt;(&amp;str,u16)&gt;&gt;::try_from::_{{closure}} (1 samples, 5.00%)</title><rect x="80.0000%" y="277" width="5.0000%" height="15" fill="rgb(226,81,46)" fg:x="16" fg:w="1"/><text x="80.2500%" y="287.50">&lt;std::..</text></g><g><title>getaddrinfo (1 samples, 5.00%)</title><rect x="80.0000%" y="261" width="5.0000%" height="15" fill="rgb(247,70,30)" fg:x="16" fg:w="1"/><text x="80.2500%" y="271.50">getadd..</text></g><g><title>si_addrinfo (1 samples, 5.00%)</title><rect x="80.0000%" y="245" width="5.0000%" height="15" fill="rgb(212,68,19)" fg:x="16" fg:w="1"/><text x="80.2500%" y="255.50">si_add..</text></g><g><title>search_addrinfo (1 samples, 5.00%)</title><rect x="80.0000%" y="229" width="5.0000%" height="15" fill="rgb(240,187,13)" fg:x="16" fg:w="1"/><text x="80.2500%" y="239.50">search..</text></g><g><title>mdns_addrinfo (1 samples, 5.00%)</title><rect x="80.0000%" y="213" width="5.0000%" height="15" fill="rgb(223,113,26)" fg:x="16" fg:w="1"/><text x="80.2500%" y="223.50">mdns_a..</text></g><g><title>_mdns_search_ex (1 samples, 5.00%)</title><rect x="80.0000%" y="197" width="5.0000%" height="15" fill="rgb(206,192,2)" fg:x="16" fg:w="1"/><text x="80.2500%" y="207.50">_mdns_..</text></g><g><title>_mdns_query_start (1 samples, 5.00%)</title><rect x="80.0000%" y="181" width="5.0000%" height="15" fill="rgb(241,108,4)" fg:x="16" fg:w="1"/><text x="80.2500%" y="191.50">_mdns_..</text></g><g><title>DNSServiceQueryRecordInternal (1 samples, 5.00%)</title><rect x="80.0000%" y="165" width="5.0000%" height="15" fill="rgb(247,173,49)" fg:x="16" fg:w="1"/><text x="80.2500%" y="175.50">DNSSer..</text></g><g><title>__close_nocancel (1 samples, 5.00%)</title><rect x="80.0000%" y="149" width="5.0000%" height="15" fill="rgb(224,114,35)" fg:x="16" fg:w="1"/><text x="80.2500%" y="159.50">__clos..</text></g><g><title>lapin::buffer::Buffer::grow (1 samples, 5.00%)</title><rect x="85.0000%" y="389" width="5.0000%" height="15" fill="rgb(245,159,27)" fg:x="17" fg:w="1"/><text x="85.2500%" y="399.50">lapin:..</text></g><g><title>alloc::raw_vec::RawVecInner&lt;A&gt;::reserve::do_reserve_and_handle (1 samples, 5.00%)</title><rect x="85.0000%" y="373" width="5.0000%" height="15" fill="rgb(245,172,44)" fg:x="17" fg:w="1"/><text x="85.2500%" y="383.50">alloc:..</text></g><g><title>alloc::raw_vec::finish_grow (1 samples, 5.00%)</title><rect x="85.0000%" y="357" width="5.0000%" height="15" fill="rgb(236,23,11)" fg:x="17" fg:w="1"/><text x="85.2500%" y="367.50">alloc:..</text></g><g><title>_realloc (1 samples, 5.00%)</title><rect x="85.0000%" y="341" width="5.0000%" height="15" fill="rgb(205,117,38)" fg:x="17" fg:w="1"/><text x="85.2500%" y="351.50">_reall..</text></g><g><title>_malloc_zone_realloc (1 samples, 5.00%)</title><rect x="85.0000%" y="325" width="5.0000%" height="15" fill="rgb(237,72,25)" fg:x="17" fg:w="1"/><text x="85.2500%" y="335.50">_mallo..</text></g><g><title>szone_realloc (1 samples, 5.00%)</title><rect x="85.0000%" y="309" width="5.0000%" height="15" fill="rgb(244,70,9)" fg:x="17" fg:w="1"/><text x="85.2500%" y="319.50">szone_..</text></g><g><title>szone_malloc_should_clear (1 samples, 5.00%)</title><rect x="85.0000%" y="293" width="5.0000%" height="15" fill="rgb(217,125,39)" fg:x="17" fg:w="1"/><text x="85.2500%" y="303.50">szone_..</text></g><g><title>medium_malloc_should_clear (1 samples, 5.00%)</title><rect x="85.0000%" y="277" width="5.0000%" height="15" fill="rgb(235,36,10)" fg:x="17" fg:w="1"/><text x="85.2500%" y="287.50">medium..</text></g><g><title>lapin::io_loop::IoLoop::run (2 samples, 10.00%)</title><rect x="85.0000%" y="405" width="10.0000%" height="15" fill="rgb(251,123,47)" fg:x="17" fg:w="2"/><text x="85.2500%" y="415.50">lapin::io_loop..</text></g><g><title>lapin::channels::Channels::handle_frame (1 samples, 5.00%)</title><rect x="90.0000%" y="389" width="5.0000%" height="15" fill="rgb(221,13,13)" fg:x="18" fg:w="1"/><text x="90.2500%" y="399.50">lapin:..</text></g><g><title>lapin::channel::Channel::receive_method (1 samples, 5.00%)</title><rect x="90.0000%" y="373" width="5.0000%" height="15" fill="rgb(238,131,9)" fg:x="18" fg:w="1"/><text x="90.2500%" y="383.50">lapin:..</text></g><g><title>lapin::channel::Channel::receive_queue_declare_ok (1 samples, 5.00%)</title><rect x="90.0000%" y="357" width="5.0000%" height="15" fill="rgb(211,50,8)" fg:x="18" fg:w="1"/><text x="90.2500%" y="367.50">lapin:..</text></g><g><title>pinky_swear::Pinky&lt;T&gt;::swear (1 samples, 5.00%)</title><rect x="90.0000%" y="341" width="5.0000%" height="15" fill="rgb(245,182,24)" fg:x="18" fg:w="1"/><text x="90.2500%" y="351.50">pinky_..</text></g><g><title>all (20 samples, 100%)</title><rect x="0.0000%" y="501" width="100.0000%" height="15" fill="rgb(242,14,37)" fg:x="0" fg:w="20"/><text x="0.2500%" y="511.50"></text></g><g><title>thread_start (7 samples, 35.00%)</title><rect x="65.0000%" y="485" width="35.0000%" height="15" fill="rgb(246,228,12)" fg:x="13" fg:w="7"/><text x="65.2500%" y="495.50">thread_start</text></g><g><title>_pthread_start (7 samples, 35.00%)</title><rect x="65.0000%" y="469" width="35.0000%" height="15" fill="rgb(213,55,15)" fg:x="13" fg:w="7"/><text x="65.2500%" y="479.50">_pthread_start</text></g><g><title>std::sys::pal::unix::thread::Thread::new::thread_start (7 samples, 35.00%)</title><rect x="65.0000%" y="453" width="35.0000%" height="15" fill="rgb(209,9,3)" fg:x="13" fg:w="7"/><text x="65.2500%" y="463.50">std::sys::pal::unix::thread::Thread::new::thread_start</text></g><g><title>core::ops::function::FnOnce::call_once{{vtable.shim}} (7 samples, 35.00%)</title><rect x="65.0000%" y="437" width="35.0000%" height="15" fill="rgb(230,59,30)" fg:x="13" fg:w="7"/><text x="65.2500%" y="447.50">core::ops::function::FnOnce::call_once{{vtable.shim}}</text></g><g><title>std::sys::backtrace::__rust_begin_short_backtrace (7 samples, 35.00%)</title><rect x="65.0000%" y="421" width="35.0000%" height="15" fill="rgb(209,121,21)" fg:x="13" fg:w="7"/><text x="65.2500%" y="431.50">std::sys::backtrace::__rust_begin_short_backtrace</text></g><g><title>tokio::runtime::blocking::pool::Inner::run (1 samples, 5.00%)</title><rect x="95.0000%" y="405" width="5.0000%" height="15" fill="rgb(220,109,13)" fg:x="19" fg:w="1"/><text x="95.2500%" y="415.50">tokio:..</text></g><g><title>tokio::runtime::task::harness::Harness&lt;T,S&gt;::poll (1 samples, 5.00%)</title><rect x="95.0000%" y="389" width="5.0000%" height="15" fill="rgb(232,18,1)" fg:x="19" fg:w="1"/><text x="95.2500%" y="399.50">tokio:..</text></g><g><title>tokio::runtime::task::core::Core&lt;T,S&gt;::poll (1 samples, 5.00%)</title><rect x="95.0000%" y="373" width="5.0000%" height="15" fill="rgb(215,41,42)" fg:x="19" fg:w="1"/><text x="95.2500%" y="383.50">tokio:..</text></g><g><title>&lt;tokio::runtime::blocking::task::BlockingTask&lt;T&gt; as core::future::future::Future&gt;::poll (1 samples, 5.00%)</title><rect x="95.0000%" y="357" width="5.0000%" height="15" fill="rgb(224,123,36)" fg:x="19" fg:w="1"/><text x="95.2500%" y="367.50">&lt;tokio..</text></g><g><title>&lt;(&amp;str,u16) as std::net::socket_addr::ToSocketAddrs&gt;::to_socket_addrs (1 samples, 5.00%)</title><rect x="95.0000%" y="341" width="5.0000%" height="15" fill="rgb(240,125,3)" fg:x="19" fg:w="1"/><text x="95.2500%" y="351.50">&lt;(&amp;str..</text></g><g><title>&lt;std::sys::net::connection::socket::LookupHost as core::convert::TryFrom&lt;(&amp;str,u16)&gt;&gt;::try_from::_{{closure}} (1 samples, 5.00%)</title><rect x="95.0000%" y="325" width="5.0000%" height="15" fill="rgb(205,98,50)" fg:x="19" fg:w="1"/><text x="95.2500%" y="335.50">&lt;std::..</text></g><g><title>getaddrinfo (1 samples, 5.00%)</title><rect x="95.0000%" y="309" width="5.0000%" height="15" fill="rgb(205,185,37)" fg:x="19" fg:w="1"/><text x="95.2500%" y="319.50">getadd..</text></g><g><title>si_addrinfo (1 samples, 5.00%)</title><rect x="95.0000%" y="293" width="5.0000%" height="15" fill="rgb(238,207,15)" fg:x="19" fg:w="1"/><text x="95.2500%" y="303.50">si_add..</text></g></svg></svg>