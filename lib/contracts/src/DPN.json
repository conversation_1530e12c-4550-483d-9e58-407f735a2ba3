{"_format": "hh-sol-artifact-1", "contractName": "DPN", "sourceName": "contracts/DPN.sol", "abi": [{"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "rewardsAmount", "type": "uint256"}], "name": "RewardsDistributed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "withdrawalAmount", "type": "uint256"}], "name": "WithdrawalCompleted", "type": "event"}, {"inputs": [], "name": "allowU2UWithdrawal", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}], "name": "deposit", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_withdrawalAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_rewardsAmount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>wal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "initialOwner", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isMasternode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "masterno<PERSON>", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_accept", "type": "bool"}], "name": "setAllowU2UWithdrawal", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "addresses", "type": "address[]"}], "name": "setMasternodes", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token_address", "type": "address"}], "name": "setU2DPNTokenAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "u2dpnTokenAddr", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x608060405234801561001057600080fd5b50610dea806100206000396000f3fe6080604052600436106100f35760003560e01c8063c28e6b441161008a578063e8782fc111610059578063e8782fc11461029d578063ee2b73f6146102bd578063f2fde38b146102df578063f340fa01146102ff57600080fd5b8063c28e6b441461021f578063c4d66de81461023f578063d82901e01461025f578063df18e5631461027f57600080fd5b8063715018a6116100c6578063715018a614610184578063833dd301146101995780638456cb59146101b95780638da5cb5b146101ce57600080fd5b806324f93eb6146100f85780632dbbb9f2146101285780633f4ba83a1461014a5780635c975abb1461015f575b600080fd5b34801561010457600080fd5b50600154600160a01b900460ff165b60405190151581526020015b60405180910390f35b34801561013457600080fd5b50610148610143366004610b6d565b610312565b005b34801561015657600080fd5b50610148610527565b34801561016b57600080fd5b50600080516020610d958339815191525460ff16610113565b34801561019057600080fd5b50610148610539565b3480156101a557600080fd5b506101486101b4366004610baf565b61054b565b3480156101c557600080fd5b50610148610575565b3480156101da57600080fd5b507f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300546001600160a01b03165b6040516001600160a01b03909116815260200161011f565b34801561022b57600080fd5b5061014861023a366004610bdf565b610585565b34801561024b57600080fd5b5061014861025a366004610baf565b6105ab565b34801561026b57600080fd5b5061014861027a366004610c12565b6106c3565b34801561028b57600080fd5b506001546001600160a01b0316610207565b3480156102a957600080fd5b506101136102b8366004610baf565b610791565b3480156102c957600080fd5b506102d26107fa565b60405161011f9190610cd7565b3480156102eb57600080fd5b506101486102fa366004610baf565b61085c565b61014861030d366004610baf565b61089a565b61031b33610791565b61035f5760405162461bcd60e51b815260206004820152601060248201526f4e6f742061206d61737465726e6f646560801b60448201526064015b60405180910390fd5b6103676108e8565b600154600160a01b900460ff1615610443578147116103be5760405162461bcd60e51b8152602060048201526013602482015272455843454544535f44504e5f42414c414e434560681b6044820152606401610356565b6040516001600160a01b0384169083156108fc029084906000818181858888f193505050501580156103f4573d6000803e3d6000fd5b50826001600160a01b0316846001600160a01b03167fb00382203b46c3b6ad0a2d7af0268e334bd9406256a7c7ba8f7fc8bc47f8cde98460405161043a91815260200190565b60405180910390a35b6001546001600160a01b03161580159061045d5750600081115b156105215760015460405163a9059cbb60e01b81526001600160a01b0385811660048301526024820184905290911690819063a9059cbb906044016020604051808303816000875af11580156104b7573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906104db9190610d24565b50836001600160a01b03167fdf29796aad820e4bb192f3a8d631b76519bcd2cbe77cc85af20e9df53cece0868360405161051791815260200190565b60405180910390a2505b50505050565b61052f610919565b610537610974565b565b610541610919565b61053760006109d4565b610553610919565b600180546001600160a01b0319166001600160a01b0392909216919091179055565b61057d610919565b610537610a45565b61058d610919565b60018054911515600160a01b0260ff60a01b19909216919091179055565b7ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a008054600160401b810460ff16159067ffffffffffffffff166000811580156105f15750825b905060008267ffffffffffffffff16600114801561060e5750303b155b90508115801561061c575080155b1561063a5760405163f92ee8a960e01b815260040160405180910390fd5b845467ffffffffffffffff19166001178555831561066457845460ff60401b1916600160401b1785555b61066c610a8e565b61067586610a9e565b83156106bb57845460ff60401b19168555604051600181527fc7f505b2f371ae2175ee4913f4499e1f2633a7b5936321eed1cdaeb6115181d29060200160405180910390a15b505050505050565b6106cb610919565b60008054905b8181101561071e5760008054806106ea576106ea610d41565b600082815260209020810160001990810180546001600160a01b03191690550190558061071681610d57565b9150506106d1565b5060005b825181101561078c57600083828151811061073f5761073f610d7e565b60209081029190910181015182546001810184556000938452919092200180546001600160a01b0319166001600160a01b039092169190911790558061078481610d57565b915050610722565b505050565b6000805b6000548110156107f157826001600160a01b0316600082815481106107bc576107bc610d7e565b6000918252602090912001546001600160a01b0316036107df5750600192915050565b806107e981610d57565b915050610795565b50600092915050565b6060600080548060200260200160405190810160405280929190818152602001828054801561085257602002820191906000526020600020905b81546001600160a01b03168152600190910190602001808311610834575b5050505050905090565b610864610919565b6001600160a01b03811661088e57604051631e4fbdf760e01b815260006004820152602401610356565b610897816109d4565b50565b6108a26108e8565b806001600160a01b03167fe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c346040516108dd91815260200190565b60405180910390a250565b600080516020610d958339815191525460ff16156105375760405163d93c066560e01b815260040160405180910390fd5b3361094b7f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c199300546001600160a01b031690565b6001600160a01b0316146105375760405163118cdaa760e01b8152336004820152602401610356565b61097c610aaf565b600080516020610d95833981519152805460ff191681557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa335b6040516001600160a01b03909116815260200160405180910390a150565b7f9016d09d72d40fdae2fd8ceac6b6234c7706214fd39c1cd1e609a0528c19930080546001600160a01b031981166001600160a01b03848116918217845560405192169182907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a3505050565b610a4d6108e8565b600080516020610d95833981519152805460ff191660011781557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258336109b6565b610a96610adf565b610537610b28565b610aa6610adf565b61089781610b49565b600080516020610d958339815191525460ff1661053757604051638dfc202b60e01b815260040160405180910390fd5b7ff0c57e16840df040f15088dc2f81fe391c3923bec73e23a9662efc9c229c6a0054600160401b900460ff1661053757604051631afcd79f60e31b815260040160405180910390fd5b610b30610adf565b600080516020610d95833981519152805460ff19169055565b610864610adf565b80356001600160a01b0381168114610b6857600080fd5b919050565b60008060008060808587031215610b8357600080fd5b610b8c85610b51565b9350610b9a60208601610b51565b93969395505050506040820135916060013590565b600060208284031215610bc157600080fd5b610bca82610b51565b9392505050565b801515811461089757600080fd5b600060208284031215610bf157600080fd5b8135610bca81610bd1565b634e487b7160e01b600052604160045260246000fd5b60006020808385031215610c2557600080fd5b823567ffffffffffffffff80821115610c3d57600080fd5b818501915085601f830112610c5157600080fd5b813581811115610c6357610c63610bfc565b8060051b604051601f19603f83011681018181108582111715610c8857610c88610bfc565b604052918252848201925083810185019188831115610ca657600080fd5b938501935b82851015610ccb57610cbc85610b51565b84529385019392850192610cab565b98975050505050505050565b6020808252825182820181905260009190848201906040850190845b81811015610d185783516001600160a01b031683529284019291840191600101610cf3565b50909695505050505050565b600060208284031215610d3657600080fd5b8151610bca81610bd1565b634e487b7160e01b600052603160045260246000fd5b600060018201610d7757634e487b7160e01b600052601160045260246000fd5b5060010190565b634e487b7160e01b600052603260045260246000fdfecd5ed15c6e187e77e9aee88184c21f4f2182ab5827cb3b7e07fbedcd63f03300a26469706673582212204cc339e6ffac4e1633e740dc3e9fe44be48b27cfd0062760dac18e51696715c264736f6c63430008140033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}