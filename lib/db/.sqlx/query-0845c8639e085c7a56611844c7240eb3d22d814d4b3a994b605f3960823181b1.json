{"db_name": "PostgreSQL", "query": "\n                SELECT * FROM referrals\n                WHERE user_addr = $1 AND referred_by IS NULL;\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referral_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "created_at", "type_info": "Int8"}, {"ordinal": 2, "name": "referred_at", "type_info": "Int8"}, {"ordinal": 3, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "referred_by", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text"]}, "nullable": [true, false, true, true, false, true]}, "hash": "0845c8639e085c7a56611844c7240eb3d22d814d4b3a994b605f3960823181b1"}