{"db_name": "PostgreSQL", "query": "\n                INSERT INTO transactions (\n                    tx_hash,\n                    from_addr,\n                    to_addr,\n                    amount,\n                    tx_type,\n                    tx_status,\n                    chain_tx_hash,\n                    created_at\n                ) VALUES (\n                    $1,\n                    $2,\n                    $3,\n                    $4,\n                    $5,\n                    $6,\n                    $7,\n                    $8\n                )\n                ON CONFLICT (tx_hash) DO NOTHING;\n            ", "describe": {"columns": [], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int4", "Int4", "<PERSON><PERSON><PERSON><PERSON>", "Int8"]}, "nullable": []}, "hash": "0aaf30a297035d0d55cbddb014727dcb4aad13fe38a55b24279d68e395398e30"}