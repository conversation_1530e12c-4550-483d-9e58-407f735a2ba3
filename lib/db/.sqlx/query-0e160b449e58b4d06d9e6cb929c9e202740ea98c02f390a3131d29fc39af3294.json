{"db_name": "PostgreSQL", "query": "\n                INSERT INTO users (\n                    fingerprint,\n                    pincode,\n                    email,\n                    username,\n                    email_verified,\n                    deposit_addr,\n                    withdrawal_addr,\n                    salt,\n                    password_hashed,\n                    balance,\n                    created_at,\n                    last_login\n                ) VALUES (\n                    $1,\n                    $2,\n                    $3,\n                    $4,\n                    $5,\n                    $6,\n                    $7,\n                    $8,\n                    $9,\n                    $10,\n                    $11,\n                    $12\n                )\n                ON CONFLICT (deposit_addr) DO UPDATE SET deposit_addr = $4\n                RETURNING *\n            ", "describe": {"columns": [{"ordinal": 0, "name": "fingerprint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "pincode", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "deposit_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "withdrawal_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "balance", "type_info": "Int8"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}, {"ordinal": 7, "name": "last_login", "type_info": "Int8"}, {"ordinal": 8, "name": "salt", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "password_hashed", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 10, "name": "username", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "email_verified", "type_info": "Bool"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bool", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", "Int8"]}, "nullable": [true, true, true, false, true, false, false, false, true, true, true, false]}, "hash": "0e160b449e58b4d06d9e6cb929c9e202740ea98c02f390a3131d29fc39af3294"}