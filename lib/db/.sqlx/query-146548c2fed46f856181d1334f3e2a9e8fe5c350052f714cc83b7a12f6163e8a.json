{"db_name": "PostgreSQL", "query": "\n                UPDATE user_connection_history\n                SET time_end = $2\n                WHERE user_addr = $1 AND\n                time_start = (SELECT MAX(time_start) FROM user_connection_history WHERE user_addr = $1)\n                RETURNING user_addr, time_start, time_end;\n            ", "describe": {"columns": [{"ordinal": 0, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "time_start", "type_info": "Int4"}, {"ordinal": 2, "name": "time_end", "type_info": "Int4"}], "parameters": {"Left": ["Text", "Int4"]}, "nullable": [false, false, true]}, "hash": "146548c2fed46f856181d1334f3e2a9e8fe5c350052f714cc83b7a12f6163e8a"}