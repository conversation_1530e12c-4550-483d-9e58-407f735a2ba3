{"db_name": "PostgreSQL", "query": "\n                UPDATE sessions \n                SET \n                tx_hash = $2,\n                end_at = $3,\n                duration = $4,\n                bandwidth_usage = $5,\n                duration_fee = $6,\n                bandwidth_fee = $7,\n                total_fee = $8,\n                status = $9,\n                reason = $10,\n                provider_country_id = $11,\n                total_fee_v2 = $12\n                WHERE session_hash = $1\n                RETURNING *\n            ", "describe": {"columns": [{"ordinal": 0, "name": "rate_per_kb_v2", "type_info": "Int8"}, {"ordinal": 1, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 2, "name": "handshake_at", "type_info": "Int8"}, {"ordinal": 3, "name": "end_at", "type_info": "Int8"}, {"ordinal": 4, "name": "duration", "type_info": "Int8"}, {"ordinal": 5, "name": "bandwidth_usage", "type_info": "Int8"}, {"ordinal": 6, "name": "duration_fee", "type_info": "Int8"}, {"ordinal": 7, "name": "bandwidth_fee", "type_info": "Int8"}, {"ordinal": 8, "name": "total_fee", "type_info": "Int8"}, {"ordinal": 9, "name": "status", "type_info": "Int4"}, {"ordinal": 10, "name": "reason", "type_info": "Int4"}, {"ordinal": 11, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 12, "name": "provider_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 13, "name": "client_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 14, "name": "session_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 15, "name": "client_identifier", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 16, "name": "provider_country_id", "type_info": "Int8"}, {"ordinal": 17, "name": "total_fee_v2", "type_info": "Int8"}], "parameters": {"Left": ["Text", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", "Int8", "Int8", "Int8", "Int8", "Int4", "Int4", "Int8", "Int8"]}, "nullable": [true, true, true, true, true, true, true, true, true, false, true, true, false, false, false, false, true, true]}, "hash": "1589852f247179c27f4357acac94d39fd2ace31b2209030ea993177ef0972990"}