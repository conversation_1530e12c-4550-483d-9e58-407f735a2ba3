{"db_name": "PostgreSQL", "query": "\n                INSERT INTO sessions (session_hash, provider_addr, client_addr, status, rate_per_kb_v2, rate_per_kb, handshake_at, bandwidth_usage, duration, client_identifier, provider_country_id, total_fee_v2)\n                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)\n                ON CONFLICT (session_hash) DO NOTHING\n                RETURNING *;\n                ", "describe": {"columns": [{"ordinal": 0, "name": "rate_per_kb_v2", "type_info": "Int8"}, {"ordinal": 1, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 2, "name": "handshake_at", "type_info": "Int8"}, {"ordinal": 3, "name": "end_at", "type_info": "Int8"}, {"ordinal": 4, "name": "duration", "type_info": "Int8"}, {"ordinal": 5, "name": "bandwidth_usage", "type_info": "Int8"}, {"ordinal": 6, "name": "duration_fee", "type_info": "Int8"}, {"ordinal": 7, "name": "bandwidth_fee", "type_info": "Int8"}, {"ordinal": 8, "name": "total_fee", "type_info": "Int8"}, {"ordinal": 9, "name": "status", "type_info": "Int4"}, {"ordinal": 10, "name": "reason", "type_info": "Int4"}, {"ordinal": 11, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 12, "name": "provider_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 13, "name": "client_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 14, "name": "session_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 15, "name": "client_identifier", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 16, "name": "provider_country_id", "type_info": "Int8"}, {"ordinal": 17, "name": "total_fee_v2", "type_info": "Int8"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int4", "Int8", "Int8", "Int8", "Int8", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8"]}, "nullable": [true, true, true, true, true, true, true, true, true, false, true, true, false, false, false, false, true, true]}, "hash": "1713c488150f27842f88d5e3b54b13e52b86e862f2c7ec83a027d3d7833b647a"}