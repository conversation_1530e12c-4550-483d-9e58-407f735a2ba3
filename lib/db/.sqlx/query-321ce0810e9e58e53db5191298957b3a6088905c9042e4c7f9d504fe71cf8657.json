{"db_name": "PostgreSQL", "query": "\n                WITH ProviderSessionStats AS (\n                    SELECT \n                        COUNT(*) as total_sessions,\n                        SUM(bandwidth_usage) as total_bandwidth_usages\n                    FROM sessions\n                    WHERE provider_addr = $1\n                )\n                SELECT \n                    (SELECT total_sessions FROM ProviderSessionStats) AS total_sessions,\n                    (SELECT total_bandwidth_usages FROM ProviderSessionStats) AS total_bandwidth_usages,\n                    (SELECT sum(amount) FROM internal_transactions\n                        WHERE to_addr = $1 AND tx_status = $2 AND tx_type = $3) AS total_network_rewards\n            ", "describe": {"columns": [{"ordinal": 0, "name": "total_sessions", "type_info": "Int8"}, {"ordinal": 1, "name": "total_bandwidth_usages", "type_info": "Numeric"}, {"ordinal": 2, "name": "total_network_rewards", "type_info": "Numeric"}], "parameters": {"Left": ["Text", "Int4", "Int4"]}, "nullable": [null, null, null]}, "hash": "321ce0810e9e58e53db5191298957b3a6088905c9042e4c7f9d504fe71cf8657"}