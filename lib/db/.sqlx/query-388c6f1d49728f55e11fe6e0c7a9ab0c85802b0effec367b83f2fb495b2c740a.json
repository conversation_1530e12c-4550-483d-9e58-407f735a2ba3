{"db_name": "PostgreSQL", "query": "\n                SELECT *\n                FROM user_tiers\n                WHERE user_addr = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "points", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false]}, "hash": "388c6f1d49728f55e11fe6e0c7a9ab0c85802b0effec367b83f2fb495b2c740a"}