{"db_name": "PostgreSQL", "query": "\n                UPDATE proxy_accounts\n                SET ip_rotation_period = $3, \n                    whitelisted_ip = $4,\n                    rate_per_kb = $5, \n                    rate_per_second = $6, \n                    country_geoname_id = $7,\n                    city_geoname_id = $8,\n                    prioritized_ip = $9,\n                    prioritized_ip_level = $10\n                WHERE id = $1 AND user_addr = $2\n                RETURNING *\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "passwd", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "ip_rotation_period", "type_info": "Int8"}, {"ordinal": 3, "name": "whitelisted_ip", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 6, "name": "rate_per_second", "type_info": "Int8"}, {"ordinal": 7, "name": "city_geoname_id", "type_info": "Int8"}, {"ordinal": 8, "name": "country_geoname_id", "type_info": "Int8"}, {"ordinal": 9, "name": "created_at", "type_info": "Int8"}, {"ordinal": 10, "name": "prioritized_ip", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "prioritized_ip_level", "type_info": "Int2"}], "parameters": {"Left": ["Text", "Text", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", "Int8", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2"]}, "nullable": [false, false, false, true, false, false, false, true, false, false, true, true]}, "hash": "3b5a375e415485a506a1f9dd025b4daf6e7a9e983cb2e1eb794db0dab1993507"}