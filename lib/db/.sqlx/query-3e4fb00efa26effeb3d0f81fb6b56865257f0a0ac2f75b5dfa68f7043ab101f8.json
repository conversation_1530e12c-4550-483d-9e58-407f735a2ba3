{"db_name": "PostgreSQL", "query": "\n                SELECT country_geoname_id, country_name, bonus_amount\n                FROM bonus_config\n            ", "describe": {"columns": [{"ordinal": 0, "name": "country_geoname_id", "type_info": "Int4"}, {"ordinal": 1, "name": "country_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "bonus_amount", "type_info": "Float8"}], "parameters": {"Left": []}, "nullable": [false, false, false]}, "hash": "3e4fb00efa26effeb3d0f81fb6b56865257f0a0ac2f75b5dfa68f7043ab101f8"}