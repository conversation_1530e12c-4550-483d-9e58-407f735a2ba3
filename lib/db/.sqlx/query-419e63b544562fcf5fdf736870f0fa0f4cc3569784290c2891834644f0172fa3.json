{"db_name": "PostgreSQL", "query": "\n                SELECT * FROM user_bandwidth_price\n                WHERE user_addr = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 1, "name": "rate_per_second", "type_info": "Int8"}, {"ordinal": 2, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false]}, "hash": "419e63b544562fcf5fdf736870f0fa0f4cc3569784290c2891834644f0172fa3"}