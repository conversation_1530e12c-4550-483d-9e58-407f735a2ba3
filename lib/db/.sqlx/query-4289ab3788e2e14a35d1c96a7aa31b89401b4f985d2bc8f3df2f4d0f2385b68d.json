{"db_name": "PostgreSQL", "query": "\n                SELECT *\n                FROM user_tier_points\n                WHERE user_addr = $1\n                ORDER BY created_at DESC\n                LIMIT 20;\n            ", "describe": {"columns": [{"ordinal": 0, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "points", "type_info": "Int8"}, {"ordinal": 2, "name": "created_at", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false]}, "hash": "4289ab3788e2e14a35d1c96a7aa31b89401b4f985d2bc8f3df2f4d0f2385b68d"}