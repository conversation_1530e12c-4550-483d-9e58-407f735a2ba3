{"db_name": "PostgreSQL", "query": "\n                SELECT * FROM region_info_history\n            ", "describe": {"columns": [{"ordinal": 0, "name": "geoname_id", "type_info": "Int8"}, {"ordinal": 1, "name": "is_country", "type_info": "Bool"}, {"ordinal": 2, "name": "name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "country_geoname_id", "type_info": "Int8"}, {"ordinal": 4, "name": "country_geoname_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": []}, "nullable": [false, false, false, true, true]}, "hash": "52d1c8f92ea99b4850804a744f6d6c3c927bf1e0be87d78f36102ab1bd72b553"}