{"db_name": "PostgreSQL", "query": "\n                SELECT * \n                FROM sessions \n                WHERE session_hash = $1\n                LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "rate_per_kb_v2", "type_info": "Int8"}, {"ordinal": 1, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 2, "name": "handshake_at", "type_info": "Int8"}, {"ordinal": 3, "name": "end_at", "type_info": "Int8"}, {"ordinal": 4, "name": "duration", "type_info": "Int8"}, {"ordinal": 5, "name": "bandwidth_usage", "type_info": "Int8"}, {"ordinal": 6, "name": "duration_fee", "type_info": "Int8"}, {"ordinal": 7, "name": "bandwidth_fee", "type_info": "Int8"}, {"ordinal": 8, "name": "total_fee", "type_info": "Int8"}, {"ordinal": 9, "name": "status", "type_info": "Int4"}, {"ordinal": 10, "name": "reason", "type_info": "Int4"}, {"ordinal": 11, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 12, "name": "provider_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 13, "name": "client_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 14, "name": "session_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 15, "name": "client_identifier", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 16, "name": "provider_country_id", "type_info": "Int8"}, {"ordinal": 17, "name": "total_fee_v2", "type_info": "Int8"}], "parameters": {"Left": ["Text"]}, "nullable": [true, true, true, true, true, true, true, true, true, false, true, true, false, false, false, false, true, true]}, "hash": "55f88bba7c27e38a85ad9e015f53208bd1bf841b28aed7d3d5fb88dee4af5508"}