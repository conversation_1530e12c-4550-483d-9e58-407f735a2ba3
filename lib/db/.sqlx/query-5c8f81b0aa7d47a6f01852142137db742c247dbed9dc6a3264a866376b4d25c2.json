{"db_name": "PostgreSQL", "query": "\n                UPDATE transactions \n                SET \n                    chain_tx_hash = $2, \n                    amount = $3, \n                    tx_status = $4\n                WHERE tx_hash = $1\n                RETURNING *\n            ", "describe": {"columns": [{"ordinal": 0, "name": "from_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "to_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "amount", "type_info": "Int8"}, {"ordinal": 3, "name": "chain_tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "tx_type", "type_info": "Int4"}, {"ordinal": 5, "name": "tx_status", "type_info": "Int4"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}, {"ordinal": 7, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int4"]}, "nullable": [false, false, false, true, false, false, false, false]}, "hash": "5c8f81b0aa7d47a6f01852142137db742c247dbed9dc6a3264a866376b4d25c2"}