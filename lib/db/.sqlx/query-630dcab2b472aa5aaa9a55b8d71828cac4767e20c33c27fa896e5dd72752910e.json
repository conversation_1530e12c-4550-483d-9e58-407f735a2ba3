{"db_name": "PostgreSQL", "query": "\n                SELECT * FROM referrals\n                WHERE referral_code = $1;\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referral_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "created_at", "type_info": "Int8"}, {"ordinal": 2, "name": "referred_at", "type_info": "Int8"}, {"ordinal": 3, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "referred_by", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text"]}, "nullable": [true, false, true, true, false, true]}, "hash": "630dcab2b472aa5aaa9a55b8d71828cac4767e20c33c27fa896e5dd72752910e"}