{"db_name": "PostgreSQL", "query": "\n                SELECT * \n                FROM sessions \n                WHERE status = $1\n                ORDER BY handshake_at DESC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "rate_per_kb_v2", "type_info": "Int8"}, {"ordinal": 1, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 2, "name": "handshake_at", "type_info": "Int8"}, {"ordinal": 3, "name": "end_at", "type_info": "Int8"}, {"ordinal": 4, "name": "duration", "type_info": "Int8"}, {"ordinal": 5, "name": "bandwidth_usage", "type_info": "Int8"}, {"ordinal": 6, "name": "duration_fee", "type_info": "Int8"}, {"ordinal": 7, "name": "bandwidth_fee", "type_info": "Int8"}, {"ordinal": 8, "name": "total_fee", "type_info": "Int8"}, {"ordinal": 9, "name": "status", "type_info": "Int4"}, {"ordinal": 10, "name": "reason", "type_info": "Int4"}, {"ordinal": 11, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 12, "name": "provider_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 13, "name": "client_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 14, "name": "session_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 15, "name": "client_identifier", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 16, "name": "provider_country_id", "type_info": "Int8"}, {"ordinal": 17, "name": "total_fee_v2", "type_info": "Int8"}], "parameters": {"Left": ["Int4"]}, "nullable": [true, true, true, true, true, true, true, true, true, false, true, true, false, false, false, false, true, true]}, "hash": "64b7a5df95becb7db3ed7bb70b64859502d0315b29afeaaf79fe4b6bd01cefdf"}