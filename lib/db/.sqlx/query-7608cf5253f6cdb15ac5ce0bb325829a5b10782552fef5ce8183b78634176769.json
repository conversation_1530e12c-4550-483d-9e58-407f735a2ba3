{"db_name": "PostgreSQL", "query": "\n                SELECT * \n                FROM referrals \n                WHERE user_addr = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referral_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "created_at", "type_info": "Int8"}, {"ordinal": 2, "name": "referred_at", "type_info": "Int8"}, {"ordinal": 3, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "referred_by", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text"]}, "nullable": [true, false, true, true, false, true]}, "hash": "7608cf5253f6cdb15ac5ce0bb325829a5b10782552fef5ce8183b78634176769"}