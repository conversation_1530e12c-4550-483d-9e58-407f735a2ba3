{"db_name": "PostgreSQL", "query": "\n                SELECT user_addr, referred_by\n                FROM referrals\n                WHERE referral_code = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "referred_by", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text"]}, "nullable": [false, true]}, "hash": "77aaf545d9e94907ee719a3b807342a06f012a291f67a0530a56a3dee36928df"}