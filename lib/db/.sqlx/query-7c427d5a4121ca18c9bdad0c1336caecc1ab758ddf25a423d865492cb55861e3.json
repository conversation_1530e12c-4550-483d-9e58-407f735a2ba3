{"db_name": "PostgreSQL", "query": "\n                WITH LastSuccessfulWithdrawalTransaction AS (\n                    SELECT COALESCE(MAX(created_at), 0) AS last_successful_withdrawal_timestamp\n                    FROM transactions\n                    WHERE from_addr = $1 AND\n                        tx_type = $2 AND\n                        tx_status = $4\n                )\n                SELECT SUM(amount) as rewards_amount\n                FROM internal_transactions\n                WHERE to_addr = $1 \n                    AND tx_type = $3 \n                    AND created_at >= (SELECT last_successful_withdrawal_timestamp \n                                    FROM LastSuccessfulWithdrawalTransaction);\n            ", "describe": {"columns": [{"ordinal": 0, "name": "rewards_amount", "type_info": "Numeric"}], "parameters": {"Left": ["Text", "Int4", "Int4", "Int4"]}, "nullable": [null]}, "hash": "7c427d5a4121ca18c9bdad0c1336caecc1ab758ddf25a423d865492cb55861e3"}