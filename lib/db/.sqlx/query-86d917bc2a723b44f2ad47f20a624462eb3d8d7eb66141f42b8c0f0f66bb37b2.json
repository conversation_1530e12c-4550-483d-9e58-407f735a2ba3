{"db_name": "PostgreSQL", "query": "\n                SELECT * \n                FROM referrals \n                WHERE referred_by = $1\n                ORDER BY referred_at DESC\n                LIMIT 20;\n            ", "describe": {"columns": [{"ordinal": 0, "name": "referral_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "created_at", "type_info": "Int8"}, {"ordinal": 2, "name": "referred_at", "type_info": "Int8"}, {"ordinal": 3, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "referred_by", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text"]}, "nullable": [true, false, true, true, false, true]}, "hash": "86d917bc2a723b44f2ad47f20a624462eb3d8d7eb66141f42b8c0f0f66bb37b2"}