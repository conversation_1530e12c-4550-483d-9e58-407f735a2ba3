{"db_name": "PostgreSQL", "query": " \n                SELECT\n                    (SELECT count(*) FROM referrals \n                        WHERE referred_by = $1) AS total_referees,\n                    (SELECT COUNT(*) FROM internal_transactions\n                        WHERE to_addr = $1 \n                        AND tx_type = $3 \n                        AND tx_status = $4\n                    ) AS total_commission_txs,\n                    (   \n                        SELECT SUM(amount) as rewards_amount\n                        FROM internal_transactions\n                        WHERE to_addr = $1 AND\n                            tx_type = $3 AND \n                            tx_status = $4\n                    ) AS total_commision,\n                    (\n                        WITH LastSuccessfulWithdrawalTransaction AS (\n                            SELECT COALESCE(MAX(created_at), 0) AS last_successful_withdrawal_timestamp\n                            FROM transactions\n                            WHERE from_addr = $1 AND \n                                tx_type = $2 AND \n                                tx_status = $4\n                        )\n                        SELECT SUM(amount) as rewards_amount\n                        FROM internal_transactions\n                        WHERE to_addr = $1 AND\n                            tx_type = $3 AND\n                            tx_status = $4 AND\n                            created_at >= (SELECT last_successful_withdrawal_timestamp \n                                            FROM LastSuccessfulWithdrawalTransaction)\n                    ) AS unclaimed_commission\n            ", "describe": {"columns": [{"ordinal": 0, "name": "total_referees", "type_info": "Int8"}, {"ordinal": 1, "name": "total_commission_txs", "type_info": "Int8"}, {"ordinal": 2, "name": "total_commision", "type_info": "Numeric"}, {"ordinal": 3, "name": "unclaimed_commission", "type_info": "Numeric"}], "parameters": {"Left": ["Text", "Int4", "Int4", "Int4"]}, "nullable": [null, null, null, null]}, "hash": "88935142d6a01fb75fc1d5783bdcfb76dca396f5bc4c7e8611ae4b11c78a6125"}