{"db_name": "PostgreSQL", "query": "\n                SELECT continent_geoname_id, continent_name, continent_code\n                FROM locations\n                GROUP BY continent_geoname_id, continent_name, continent_code\n                ORDER BY continent_code ASC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "continent_geoname_id", "type_info": "Int4"}, {"ordinal": 1, "name": "continent_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "continent_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": []}, "nullable": [false, false, false]}, "hash": "8b4d3df6ed30dfbd85b3f7f6dd2690bb53c6de9b84b7d3d2bf97b6288a6c2801"}