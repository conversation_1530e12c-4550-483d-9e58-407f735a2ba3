{"db_name": "PostgreSQL", "query": "\n                SELECT *\n                FROM bonus_config\n                WHERE country_geoname_id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "country_geoname_id", "type_info": "Int4"}, {"ordinal": 1, "name": "country_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "bonus_amount", "type_info": "Float8"}, {"ordinal": 3, "name": "created_at", "type_info": "Timestamp"}, {"ordinal": 4, "name": "updated_at", "type_info": "Timestamp"}], "parameters": {"Left": ["Int4"]}, "nullable": [false, false, false, false, false]}, "hash": "8c24a256d7b51525525840461ba4a0827e6b69e033283ff42af8ab328cdd502d"}