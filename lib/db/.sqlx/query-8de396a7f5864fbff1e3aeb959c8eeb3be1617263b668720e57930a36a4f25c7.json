{"db_name": "PostgreSQL", "query": "\n                select * \n                from sg_meta \n                where id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Int4"}, {"ordinal": 1, "name": "c_head", "type_info": "Int8"}, {"ordinal": 2, "name": "b_from", "type_info": "Int8"}, {"ordinal": 3, "name": "b_to", "type_info": "Int8"}], "parameters": {"Left": ["Int4"]}, "nullable": [false, false, false, false]}, "hash": "8de396a7f5864fbff1e3aeb959c8eeb3be1617263b668720e57930a36a4f25c7"}