{"db_name": "PostgreSQL", "query": "\n                SELECT *\n                FROM transactions \n                WHERE tx_status = $1\n                ORDER BY created_at ASC\n                LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "from_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "to_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "amount", "type_info": "Int8"}, {"ordinal": 3, "name": "chain_tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "tx_type", "type_info": "Int4"}, {"ordinal": 5, "name": "tx_status", "type_info": "Int4"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}, {"ordinal": 7, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Int4"]}, "nullable": [false, false, false, true, false, false, false, false]}, "hash": "926e613b9053c5b511322d370b75a2cd86923f637d0331c3906a1d08d3433f67"}