{"db_name": "PostgreSQL", "query": " \n                SELECT\n                    (SELECT sum(amount) FROM internal_transactions\n                    WHERE to_addr = $1 AND tx_status = $2) AS total_rewards,\n                    (\n                        WITH LastSuccessfulWithdrawalTransaction AS (\n                            SELECT COALESCE(MAX(created_at), 0) AS last_successful_withdrawal_timestamp\n                            FROM transactions\n                            WHERE from_addr = $1 AND tx_status = $2 AND tx_type = $3\n                        )\n                        SELECT COALESCE(SUM(amount), 0) as rewards_amount\n                        FROM internal_transactions\n                        WHERE to_addr = $1 and created_at >= (SELECT last_successful_withdrawal_timestamp FROM LastSuccessfulWithdrawalTransaction)\n                    ) AS unclaimed_rewards,\n                    (SELECT sum(amount) FROM internal_transactions\n                    WHERE to_addr = $1 AND tx_status = $2 AND tx_type = $4) AS total_network_rewards,\n                    (SELECT sum(amount) FROM internal_transactions\n                    WHERE to_addr = $1 AND tx_status = $2 AND tx_type = $5) AS total_task_rewards,\n                    (SELECT sum(amount) FROM internal_transactions\n                    WHERE to_addr = $1 AND tx_status = $2 AND tx_type = $6) AS total_referral_rewards,\n                    (SELECT sum(amount) FROM internal_transactions\n                    WHERE to_addr = $1 AND tx_status = $2 AND tx_type = $7) AS total_commission_rewards\n            ", "describe": {"columns": [{"ordinal": 0, "name": "total_rewards", "type_info": "Numeric"}, {"ordinal": 1, "name": "unclaimed_rewards", "type_info": "Numeric"}, {"ordinal": 2, "name": "total_network_rewards", "type_info": "Numeric"}, {"ordinal": 3, "name": "total_task_rewards", "type_info": "Numeric"}, {"ordinal": 4, "name": "total_referral_rewards", "type_info": "Numeric"}, {"ordinal": 5, "name": "total_commission_rewards", "type_info": "Numeric"}], "parameters": {"Left": ["Text", "Int4", "Int4", "Int4", "Int4", "Int4", "Int4"]}, "nullable": [null, null, null, null, null, null]}, "hash": "95d136acfb6a97793e5e248f1c8aa9224c9f0a1d9b2c669fae74efb3ece46bb0"}