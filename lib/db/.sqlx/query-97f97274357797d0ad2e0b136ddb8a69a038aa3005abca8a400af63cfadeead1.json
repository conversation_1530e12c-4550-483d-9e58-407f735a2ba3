{"db_name": "PostgreSQL", "query": "\n                SELECT country_geoname_id, country_name, country_iso_code\n                FROM locations\n                WHERE continent_geoname_id = $1\n                GROUP BY country_geoname_id, country_name, country_iso_code\n                ORDER BY country_name ASC\n            ", "describe": {"columns": [{"ordinal": 0, "name": "country_geoname_id", "type_info": "Int4"}, {"ordinal": 1, "name": "country_name", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "country_iso_code", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Int4"]}, "nullable": [false, false, false]}, "hash": "97f97274357797d0ad2e0b136ddb8a69a038aa3005abca8a400af63cfadeead1"}