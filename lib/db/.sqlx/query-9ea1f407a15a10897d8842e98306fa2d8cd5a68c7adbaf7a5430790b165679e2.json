{"db_name": "PostgreSQL", "query": "\n                SELECT * \n                FROM transactions\n                WHERE from_addr = $1 AND tx_type = $2\n                ORDER BY created_at DESC LIMIT 20\n            ", "describe": {"columns": [{"ordinal": 0, "name": "from_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "to_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "amount", "type_info": "Int8"}, {"ordinal": 3, "name": "chain_tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "tx_type", "type_info": "Int4"}, {"ordinal": 5, "name": "tx_status", "type_info": "Int4"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}, {"ordinal": 7, "name": "tx_hash", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}], "parameters": {"Left": ["Text", "Int4"]}, "nullable": [false, false, false, true, false, false, false, false]}, "hash": "9ea1f407a15a10897d8842e98306fa2d8cd5a68c7adbaf7a5430790b165679e2"}