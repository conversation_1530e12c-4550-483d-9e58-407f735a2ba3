{"db_name": "PostgreSQL", "query": "\n                SELECT * \n                FROM users \n                WHERE email = $1\n                OR username = $1\n                LIMIT 1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "fingerprint", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "pincode", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "email", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "deposit_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "withdrawal_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "balance", "type_info": "Int8"}, {"ordinal": 6, "name": "created_at", "type_info": "Int8"}, {"ordinal": 7, "name": "last_login", "type_info": "Int8"}, {"ordinal": 8, "name": "salt", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "password_hashed", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 10, "name": "username", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "email_verified", "type_info": "Bool"}], "parameters": {"Left": ["Text"]}, "nullable": [true, true, true, false, true, false, false, false, true, true, true, false]}, "hash": "a2ab1b130eeb0634dd74a5e6c6df3d99e411b81013dbe674737ff2ab9c089c39"}