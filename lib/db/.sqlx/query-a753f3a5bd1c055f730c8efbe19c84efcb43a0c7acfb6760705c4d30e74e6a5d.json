{"db_name": "PostgreSQL", "query": "\n                SELECT *\n                FROM proxy_accounts\n                WHERE whitelisted_ip = $1;\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "passwd", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "ip_rotation_period", "type_info": "Int8"}, {"ordinal": 3, "name": "whitelisted_ip", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 6, "name": "rate_per_second", "type_info": "Int8"}, {"ordinal": 7, "name": "city_geoname_id", "type_info": "Int8"}, {"ordinal": 8, "name": "country_geoname_id", "type_info": "Int8"}, {"ordinal": 9, "name": "created_at", "type_info": "Int8"}, {"ordinal": 10, "name": "prioritized_ip", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "prioritized_ip_level", "type_info": "Int2"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, true, false, false, false, true, false, false, true, true]}, "hash": "a753f3a5bd1c055f730c8efbe19c84efcb43a0c7acfb6760705c4d30e74e6a5d"}