{"db_name": "PostgreSQL", "query": "\n                WITH session_served as (\n                    SELECT client_identifier, bandwidth_usage\n                    FROM sessions\n                    WHERE client_addr = $1\n                )\n                SELECT pa.user_addr as user_addr, pa.id as username, pa.passwd as passwd,\n                pa.whitelisted_ip as whitelisted_ip,\n                pa.ip_rotation_period as ip_rotation_period,\n                pa.country_geoname_id as country_geoname_id,\n                pa.rate_per_kb as rate_per_kb, pa.rate_per_second as rate_per_second,\n                pa.prioritized_ip as prioritized_ip, pa.prioritized_ip_level as prioritized_ip_level,\n                SUM(s.bandwidth_usage) as bandwidth\n                FROM proxy_accounts pa\n                LEFT JOIN session_served s ON pa.id = s.client_identifier\n                WHERE pa.user_addr = $1\n                GROUP BY user_addr, username, passwd, whitelisted_ip, ip_rotation_period,\n                country_geoname_id, rate_per_kb, rate_per_second,\n                prioritized_ip, prioritized_ip_level\n            ", "describe": {"columns": [{"ordinal": 0, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "username", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "passwd", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 3, "name": "whitelisted_ip", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "ip_rotation_period", "type_info": "Int8"}, {"ordinal": 5, "name": "country_geoname_id", "type_info": "Int8"}, {"ordinal": 6, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 7, "name": "rate_per_second", "type_info": "Int8"}, {"ordinal": 8, "name": "prioritized_ip", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 9, "name": "prioritized_ip_level", "type_info": "Int2"}, {"ordinal": 10, "name": "bandwidth", "type_info": "Numeric"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, true, false, false, false, false, true, true, null]}, "hash": "e6193675539df751b6df96072859b58b9baf7e7842749400312179a538fe0663"}