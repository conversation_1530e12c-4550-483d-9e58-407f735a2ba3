{"db_name": "PostgreSQL", "query": "\n                SELECT COUNT(*) as total_pending_withdrawals\n                FROM transactions\n                WHERE from_addr = $1 AND tx_type = $2 AND tx_status = $3\n            ", "describe": {"columns": [{"ordinal": 0, "name": "total_pending_withdrawals", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Int4", "Int4"]}, "nullable": [null]}, "hash": "eeab913427a83ab622f1269e6edef0b1be6b0715d9b9e6d077ab47280c85e1ec"}