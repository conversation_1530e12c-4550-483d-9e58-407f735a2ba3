{"db_name": "PostgreSQL", "query": "\n                INSERT INTO proxy_accounts (\n                    id,\n                    passwd,\n                    ip_rotation_period,\n                    whitelisted_ip,\n                    user_addr,\n                    rate_per_kb,\n                    rate_per_second,\n                    country_geoname_id,\n                    prioritized_ip,\n                    prioritized_ip_level,\n                    created_at\n                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)\n                 ON CONFLICT (id) DO UPDATE SET\n                    passwd = EXCLUDED.passwd,\n                    ip_rotation_period = EXCLUDED.ip_rotation_period,\n                    whitelisted_ip = EXCLUDED.whitelisted_ip,\n                    user_addr = EXCLUDED.user_addr,\n                    rate_per_kb = EXCLUDED.rate_per_kb,\n                    rate_per_second = EXCLUDED.rate_per_second,\n                    country_geoname_id = EXCLUDED.country_geoname_id,\n                    prioritized_ip = EXCLUDED.prioritized_ip,\n                    prioritized_ip_level = EXCLUDED.prioritized_ip_level\n                RETURNING *;\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "passwd", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 2, "name": "ip_rotation_period", "type_info": "Int8"}, {"ordinal": 3, "name": "whitelisted_ip", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 4, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 5, "name": "rate_per_kb", "type_info": "Int8"}, {"ordinal": 6, "name": "rate_per_second", "type_info": "Int8"}, {"ordinal": 7, "name": "city_geoname_id", "type_info": "Int8"}, {"ordinal": 8, "name": "country_geoname_id", "type_info": "Int8"}, {"ordinal": 9, "name": "created_at", "type_info": "Int8"}, {"ordinal": 10, "name": "prioritized_ip", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 11, "name": "prioritized_ip_level", "type_info": "Int2"}], "parameters": {"Left": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Int8", "Int8", "Int8", "<PERSON><PERSON><PERSON><PERSON>", "Int2", "Int8"]}, "nullable": [false, false, false, true, false, false, false, true, false, false, true, true]}, "hash": "f1ec205fc4a321833d5a55b98d4087032088c09036ff4980f4e31847052724b1"}