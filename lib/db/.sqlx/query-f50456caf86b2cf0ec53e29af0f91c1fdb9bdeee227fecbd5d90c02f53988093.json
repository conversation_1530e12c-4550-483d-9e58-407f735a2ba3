{"db_name": "PostgreSQL", "query": "\n            SELECT user_addr, earned_lp, start_time, end_time, created_at, updated_at\n            FROM user_online_sessions\n            WHERE user_addr = $1 AND start_time = $2\n            ", "describe": {"columns": [{"ordinal": 0, "name": "user_addr", "type_info": "<PERSON><PERSON><PERSON><PERSON>"}, {"ordinal": 1, "name": "earned_lp", "type_info": "Float8"}, {"ordinal": 2, "name": "start_time", "type_info": "Int8"}, {"ordinal": 3, "name": "end_time", "type_info": "Int8"}, {"ordinal": 4, "name": "created_at", "type_info": "Int8"}, {"ordinal": 5, "name": "updated_at", "type_info": "Int8"}], "parameters": {"Left": ["Text", "Int8"]}, "nullable": [false, true, false, true, true, true]}, "hash": "f50456caf86b2cf0ec53e29af0f91c1fdb9bdeee227fecbd5d90c02f53988093"}