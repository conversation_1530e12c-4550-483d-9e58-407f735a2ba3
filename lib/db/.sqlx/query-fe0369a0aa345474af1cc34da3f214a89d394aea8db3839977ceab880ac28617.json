{"db_name": "PostgreSQL", "query": "\n                WITH LastSuccessfulWithdrawalTransaction AS (\n                    SELECT COALESCE(MAX(created_at), 0) AS last_successful_withdrawal_timestamp\n                    FROM transactions\n                    WHERE from_addr = $1 AND \n                        tx_type = $2 AND\n                        tx_status = $3\n                )\n                SELECT SUM(amount) as rewards_amount\n                FROM internal_transactions\n                WHERE to_addr = $1 and created_at >= (SELECT last_successful_withdrawal_timestamp FROM LastSuccessfulWithdrawalTransaction);\n            ", "describe": {"columns": [{"ordinal": 0, "name": "rewards_amount", "type_info": "Numeric"}], "parameters": {"Left": ["Text", "Int4", "Int4"]}, "nullable": [null]}, "hash": "fe0369a0aa345474af1cc34da3f214a89d394aea8db3839977ceab880ac28617"}