[package]
name = "dpn_db"
version = "0.1.0"
edition = "2021"

[dependencies]
dpn_core = { git = "https://github.com/unicornultralabs/subnet-dpn-core", branch = "main" }

tokio = { version = "1.32.0", features = ["full"] }
ethers = "2.0.10"
anyhow = "1.0.75"
sqlx = { version = "0.7.3", default-features = false, features = [
    "runtime-tokio-native-tls",
    "macros",
    "postgres",
    "bigdecimal",
    "chrono",
    "json",
    "migrate",
    "ipnetwork",
] }
bigdecimal = "0.2.2"
num = { version = "0.3.1", features = ["serde"] }
num-derive = "0.4.1"
num-traits = "0.2.17"
log = "0.4.20"
hex = "0.4"
serde = { version = "1.0.183", features = ["derive"] }
utoipa = { version = "4.1.0", features = ["actix_extras"] }
