-- Add up migration script here
TRUNCATE TABLE public.referrals;
ALTER TABLE public.referrals DROP CONSTRAINT referrals_tx_id_fkey;
ALTER TABLE public.referrals DROP COLUMN tx_id;

TRUNCATE TABLE public.sessions;
ALTER TABLE public.sessions DROP CONSTRAINT sessions_tx_id_fkey;
ALTER TABLE public.sessions DROP COLUMN tx_id;

TRUNCATE TABLE public.internal_transactions;
ALTER TABLE public.internal_transactions DROP CONSTRAINT internal_transactions_from_user_id_fkey;
ALTER TABLE public.internal_transactions DROP CONSTRAINT internal_transactions_to_user_id_fkey;
ALTER TABLE public.internal_transactions DROP COLUMN from_user_id;
ALTER TABLE public.internal_transactions DROP COLUMN to_user_id;
ALTER TABLE public.internal_transactions DROP CONSTRAINT internal_transactions_pkey;
ALTER TABLE public.internal_transactions DROP COLUMN id;
ALTER TABLE public.internal_transactions ADD tx_hash varchar(66) PRIMARY KEY;
ALTER TABLE public.internal_transactions ADD from_addr varchar(42) NOT NULL;
ALTER TABLE public.internal_transactions ADD to_addr varchar(42) NOT NULL;
ALTER TABLE public.internal_transactions ADD CONSTRAINT internal_transactions_from_addr_fkey FOREIGN KEY (from_addr) REFERENCES users (deposit_addr);
ALTER TABLE public.internal_transactions ADD CONSTRAINT internal_transactions_to_addr_fkey FOREIGN KEY (to_addr) REFERENCES users (deposit_addr);

ALTER TABLE public.referrals ADD tx_hash varchar(66) NULL;
ALTER TABLE public.referrals ADD CONSTRAINT referrals_tx_hash_fkey FOREIGN KEY (tx_hash) REFERENCES public.internal_transactions(tx_hash);

ALTER TABLE public.sessions ADD tx_hash varchar(66) NULL;
ALTER TABLE public.sessions ADD CONSTRAINT sessions_tx_hash_fkey FOREIGN KEY (tx_hash) REFERENCES public.internal_transactions(tx_hash);