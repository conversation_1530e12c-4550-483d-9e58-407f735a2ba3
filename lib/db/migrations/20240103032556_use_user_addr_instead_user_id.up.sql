TRUNCATE TABLE public.users cascade;
ALTER TABLE public.users DROP CONSTRAINT users_pkey CASCADE;
ALTER TABLE public.users DROP COLUMN id;
ALTER TABLE public.users DROP CONSTRAINT users_deposit_addr_key CASCADE;
ALTER TABLE public.users ADD CONSTRAINT users_pkey PRIMARY KEY (deposit_addr);

TRUNCATE TABLE public.transactions;
ALTER TABLE public.transactions DROP COLUMN user_id;

TRUNCATE TABLE public.internal_transactions cascade;
ALTER TABLE public.internal_transactions DROP COLUMN from_addr;
ALTER TABLE public.internal_transactions DROP COLUMN to_addr;

TRUNCATE TABLE public.sessions;
ALTER TABLE public.sessions DROP COLUMN provider_id;
ALTER TABLE public.sessions DROP COLUMN client_id;

TRUNCATE TABLE public.referrals;
ALTER TABLE public.referrals DROP CONSTRAINT referrals_pkey;
ALTER TABLE public.referrals DROP COLUMN user_id;
ALTER TABLE public.referrals DROP COLUMN referred_by;

TRUNCATE TABLE public.user_tiers;
ALTER TABLE public.user_tiers DROP CONSTRAINT user_tiers_pkey;
ALTER TABLE public.user_tiers DROP COLUMN user_id;

TRUNCATE TABLE public.user_bandwidth_price;
ALTER TABLE public.user_bandwidth_price DROP CONSTRAINT user_bandwidth_price_pkey;
ALTER TABLE public.user_bandwidth_price DROP COLUMN user_id;

TRUNCATE TABLE public.proxy_accounts;
ALTER TABLE public.proxy_accounts DROP CONSTRAINT proxy_accounts_pkey;
ALTER TABLE public.proxy_accounts DROP COLUMN user_id;

ALTER TABLE public.sessions ADD provider_addr varchar(42) NOT NULL;
ALTER TABLE public.sessions ADD client_addr varchar(42) NOT NULL;
ALTER TABLE public.sessions ADD CONSTRAINT sessions_provider_addr_fkey FOREIGN KEY (provider_addr) REFERENCES public.users(deposit_addr);
ALTER TABLE public.sessions ADD CONSTRAINT sessions_client_addr_fkey FOREIGN KEY (client_addr) REFERENCES public.users(deposit_addr);

ALTER TABLE public.proxy_accounts ADD user_addr varchar(42) NULL;
ALTER TABLE public.proxy_accounts ADD CONSTRAINT proxy_accounts_pkey PRIMARY KEY (user_addr);
ALTER TABLE public.proxy_accounts ADD CONSTRAINT proxy_accounts_user_addr_fkey FOREIGN KEY (user_addr) REFERENCES public.users(deposit_addr);

ALTER TABLE public.user_tiers ADD user_addr varchar(42) NULL;
ALTER TABLE public.user_tiers ADD CONSTRAINT user_tiers_pkey PRIMARY KEY (user_addr);
ALTER TABLE public.user_tiers ADD CONSTRAINT user_tiers_user_addr_fkey FOREIGN KEY (user_addr) REFERENCES public.users(deposit_addr);

ALTER TABLE public.user_bandwidth_price ADD user_addr varchar(42) NULL;
ALTER TABLE public.user_bandwidth_price ADD CONSTRAINT user_bandwidth_price_pkey PRIMARY KEY (user_addr);
ALTER TABLE public.user_bandwidth_price ADD CONSTRAINT user_bandwidth_price_user_addr_fkey FOREIGN KEY (user_addr) REFERENCES public.users(deposit_addr);

ALTER TABLE public.referrals ADD user_addr varchar(42) NULL;
ALTER TABLE public.referrals ADD CONSTRAINT referrals_pkey PRIMARY KEY (user_addr);
ALTER TABLE public.referrals ADD referred_by varchar(42) NULL;
ALTER TABLE public.referrals ADD CONSTRAINT referrals_user_addr_fkey FOREIGN KEY (user_addr) REFERENCES public.users(deposit_addr);
ALTER TABLE public.referrals ADD CONSTRAINT referrals_referred_by_fkey FOREIGN KEY (referred_by) REFERENCES public.users(deposit_addr);

ALTER TABLE public.internal_transactions ADD from_addr varchar(42) NOT NULL;
ALTER TABLE public.internal_transactions ADD to_addr varchar(42) NOT NULL;
ALTER TABLE public.internal_transactions ADD CONSTRAINT internal_transactions_from_addr_fkey FOREIGN KEY (from_addr) REFERENCES users (deposit_addr);
ALTER TABLE public.internal_transactions ADD CONSTRAINT internal_transactions_to_addr_fkey FOREIGN KEY (to_addr) REFERENCES users (deposit_addr);
