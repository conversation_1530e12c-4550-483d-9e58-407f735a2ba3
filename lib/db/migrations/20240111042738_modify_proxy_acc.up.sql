-- Add up migration script here
TRUNCATE TABLE public.proxy_accounts;


ALTER TABLE public.proxy_accounts RENAME COLUMN whitelisted_ips TO whitelisted_ip;
ALTER TABLE public.proxy_accounts ALTER COLUMN whitelisted_ip DROP NOT NULL;
ALTER TABLE public.proxy_accounts ADD CONSTRAINT proxy_accounts_whitelisted_ip_unique UNIQUE (whitelisted_ip);
ALTER TABLE public.proxy_accounts DROP CONSTRAINT proxy_accounts_pkey;
ALTER TABLE public.proxy_accounts RENAME CONSTRAINT proxy_accounts_username_key TO proxy_accounts_id_key;
ALTER TABLE public.proxy_accounts RENAME COLUMN username TO id;
ALTER TABLE public.proxy_accounts ALTER COLUMN id TYPE varchar USING id::varchar;
ALTER TABLE public.proxy_accounts ALTER COLUMN ip_rotation_period TYPE int8 USING ip_rotation_period::int8;

ALTER TABLE public.proxy_accounts ADD COLUMN rate_per_kb INT8 NOT NULL;
ALTER TABLE public.proxy_accounts ADD COLUMN rate_per_second INT8 NOT NULL;
ALTER TABLE public.proxy_accounts ADD COLUMN city_geoname_id INT8 NOT NULL;
ALTER TABLE public.proxy_accounts ADD COLUMN country_geoname_id INT8 NOT NULL;
ALTER TABLE public.proxy_accounts ADD COLUMN created_at INT8 NOT NULL DEFAULT 0;
