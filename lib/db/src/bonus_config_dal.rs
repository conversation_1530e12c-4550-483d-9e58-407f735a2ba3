use crate::StorageProcessor;
use anyhow::anyhow;
use dpn_core::types::bonus_config::{BonusConfig, BonusInfo};

#[derive(Debug)]
pub struct BonusConfigDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl BonusConfigDal<'_, '_> {

    pub async fn get_all_bonus_info(
        &mut self,
    ) -> anyhow::Result<Vec<BonusInfo>> {
        match sqlx::query!(
            r#"
                SELECT country_geoname_id, country_name, bonus_amount
                FROM bonus_config
            "#
        )
        .fetch_all(self.storage.conn())
        .await
        {
            Ok(records) => Ok(records.into_iter().map(|rec| BonusInfo {
                country_geoname_id: rec.country_geoname_id as i32,
                country_name: rec.country_name,
                bonus_amount: rec.bonus_amount,
            }).collect()),
            Err(e) => {
                return Err(anyhow!(
                    "fail to execute get all bonus info query, error: {}",
                    e
                ));
            }
        }
    }
    

    pub async fn get_bonus_info_by_country(
        &mut self,
        country_geoname_id: i32,
    ) -> anyhow::Result<Option<BonusConfig>> {
        match sqlx::query!(
            r#"
                SELECT *
                FROM bonus_config
                WHERE country_geoname_id = $1
            "#, country_geoname_id
        )
        .fetch_optional(self.storage.conn())
        .await
        {
            Ok(record) => Ok(record.map(|rec| BonusConfig {
                country_geoname_id: rec.country_geoname_id as i32,
                country_name: rec.country_name,
                bonus_amount: rec.bonus_amount,
                created_at: rec.created_at.and_utc().timestamp(),
                updated_at: rec.updated_at.and_utc().timestamp(),
            })),
            Err(e) => {
                return Err(anyhow!(
                    "fail to execute get bonus config by country query, error: {}",
                    e
                ));
            }
        }
    }

}