use anyhow::{anyhow, Context, Error};
use crate::StorageProcessor;

#[derive(Debug)] // Derive Debug for better error logging and inspection
pub struct UserTimeConnect {
    pub user_addr: String,
    pub time_start: i32,
    pub time_end: Option<i32>,
}

#[derive(Debug)]
pub struct ConnectionHistoryDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl ConnectionHistoryDal<'_, '_> {
    pub async fn create_connection_history(
        &mut self,
        time_start: i32,
        user_addr: String,
        city_geoname_id: u32,
        country_geoname_id: u32,
        ip_addr: String,
        masternode_id: String,
        login_session_id: String,
    ) -> anyhow::Result<()> {
        match sqlx::query!(
            r#"
                INSERT INTO user_connection_history
                (time_start, user_addr, ip_addr, city_geoname_id, country_geoname_id, masternode_id, login_session_id)
                VALUES($1, $2, $3, $4, $5, $6, $7);
            "#,
            time_start,
            user_addr,
            ip_addr,
            city_geoname_id as i32,
            country_geoname_id as i32,
            masternode_id,
            login_session_id
        )
        .execute(self.storage.conn())
        .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow!(
                "Failed to execute create connection history query, err={}",
                e
            )),
        }
    }

    pub async fn stop_latest_connection_history(
        &mut self,
        user_addr: String,
        time_end: i32,
    ) -> anyhow::Result<UserTimeConnect> {
        let mut transaction = self
            .storage
            .start_transaction()
            .await
            .context("start_transaction()")?;
    
        match sqlx::query!(
            r#"
                UPDATE user_connection_history
                SET time_end = $2
                WHERE user_addr = $1 AND
                time_start = (SELECT MAX(time_start) FROM user_connection_history WHERE user_addr = $1)
                RETURNING user_addr, time_start, time_end;
            "#,
            user_addr,
            time_end
        )
        .fetch_one(transaction.conn())
        .await
        {
            Ok(row) => {
                transaction.commit().await.context("commit()")?;
                let updated_record = UserTimeConnect {
                    user_addr: row.user_addr,
                    time_start: row.time_start,
                    time_end: row.time_end,
                };
                Ok(updated_record)
            }
            Err(e) => {
                transaction.commit().await.context("rollback()")?;
                Err(Error::msg(format!("failed to stop connection history: {}", e)))
            }
        }
    }

    pub async fn stop_all_connection_history_of_masternode(
        &mut self,
        time_end: i32,
    ) -> anyhow::Result<()> {
        match sqlx::query!(
            r#"
                UPDATE user_connection_history
                SET time_end = $1
                WHERE time_end IS NULL;
            "#,
            time_end
        )
        .execute(self.storage.conn())
        .await
        {
            Ok(_) => return Ok(()),
            Err(_) => return Err(Error::msg(format!("failed to stop connection history of masternode")))
        }
    }
}
