use crate::{model::storage_internal_tx::StorageInternalTx, StorageProcessor};
use anyhow::anyhow;
use dpn_core::types::internal_tx::InternalTx;

#[derive(Debug)]
pub struct InternalTxDal<'a, 'c> {
    pub storage: &'a mut StorageProcessor<'c>,
}

impl InternalTxDal<'_, '_> {
    pub async fn insert_tx(&mut self, tx: InternalTx) -> anyhow::Result<StorageInternalTx> {
        let _tx: StorageInternalTx = tx.into();

        let rec = sqlx::query!(
            r#"
            INSERT INTO internal_transactions (
                tx_hash,
                from_addr,
                to_addr,
                amount,
                tx_type,
                tx_status,
                created_at
            ) VALUES (
                $1,
                $2,
                $3,
                $4,
                $5,
                $6,
                $7
            )
            RETURNING *
        "#,
            _tx.tx_hash,
            _tx.from_addr,
            _tx.to_addr,
            _tx.amount,
            _tx.tx_type,
            _tx.tx_status,
            _tx.created_at,
        )
        .fetch_one(self.storage.conn())
        .await;

        match rec {
            Ok(rec) => Ok(StorageInternalTx {
                tx_hash: rec.tx_hash,
                from_addr: rec.from_addr,
                to_addr: rec.to_addr,
                amount: rec.amount,
                tx_type: rec.tx_type,
                tx_status: rec.tx_status,
                created_at: rec.created_at,
            }),
            Err(e) => Err(anyhow!(
                "Failed to execute insert internal transaction query, err={}",
                e
            )),
        }
    }
}
