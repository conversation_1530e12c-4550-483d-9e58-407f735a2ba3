use crate::{model::storage_location::LocationCountry, StorageProcessor};
use anyhow::anyhow;
use dpn_core::types::geo::{Continent, Country};

#[derive(Debug)]
pub struct LocationDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl LocationDal<'_, '_> {
    pub async fn get_continents(&mut self) -> anyhow::Result<Vec<Continent>> {
        match sqlx::query!(
            r#"
                SELECT continent_geoname_id, continent_name, continent_code
                FROM locations
                GROUP BY continent_geoname_id, continent_name, continent_code
                ORDER BY continent_code ASC
            "#,
        )
        .fetch_all(self.storage.conn())
        .await
        {
            Ok(continents) => Ok(continents
                .iter()
                .map(|rec| Continent {
                    geoname_id: Some(rec.continent_geoname_id.try_into().unwrap()),
                    name: Some(rec.continent_name.clone()),
                    code: Some(rec.continent_code.clone()),
                })
                .collect()),
            Err(e) => {
                return Err(anyhow!(
                    "fail to execute get all continents query, error: {}",
                    e
                ));
            }
        }
    }

    pub async fn get_countries(&mut self) -> anyhow::Result<Vec<LocationCountry>> {
        match sqlx::query!(
            r#"
                SELECT continent_code, continent_name, country_geoname_id, country_iso_code, country_name
                FROM locations
                GROUP BY continent_code, continent_name, country_geoname_id, country_iso_code, country_name
                ORDER BY continent_code ASC, country_name ASC
            "#,
        )
        .fetch_all(self.storage.conn())
        .await
        {
            Ok(countries) => Ok(countries
                .iter()
                .map(|rec| LocationCountry {
                    continent_code: rec.continent_code.clone(),
                    continent_name: rec.continent_name.clone(),
                    country_geoname_id: rec.country_geoname_id.try_into().unwrap(),
                    country_iso_code: rec.country_iso_code.clone(),
                    country_name: rec.country_name.clone(),
                })
                .collect()),
            Err(e) => {
                return Err(anyhow!(
                    "fail to execute get all countries query, error: {}",
                    e
                ));
            }
        }
    }

    pub async fn get_countries_by_continent(
        &mut self,
        continent_geoname_id: i32,
    ) -> anyhow::Result<Vec<Country>> {
        match sqlx::query!(
            r#"
                SELECT country_geoname_id, country_name, country_iso_code
                FROM locations
                WHERE continent_geoname_id = $1
                GROUP BY country_geoname_id, country_name, country_iso_code
                ORDER BY country_name ASC
            "#,
            continent_geoname_id
        )
        .fetch_all(self.storage.conn())
        .await
        {
            Ok(rec) => Ok(rec
                .iter()
                .map(|rec| Country {
                    geoname_id: Some(rec.country_geoname_id.try_into().unwrap()),
                    is_in_european_union: Some(continent_geoname_id == 6255148),
                    iso_code: Some(rec.country_iso_code.clone()),
                    name: Some(rec.country_name.clone()),
                })
                .collect()),
            Err(e) => {
                return Err(anyhow!(
                    "fail to execute get_countries_by_continent query, error: {}",
                    e
                ));
            }
        }
    }
}
