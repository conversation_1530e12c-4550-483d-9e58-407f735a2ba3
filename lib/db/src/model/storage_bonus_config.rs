use dpn_core::types::bonus_config::BonusConfig;
#[derive(Debu<PERSON>, <PERSON><PERSON>, sqlx::FromRow)]
pub struct StorageBonusConfig {
    pub country_geoname_id: i64,
    pub country_name: String,
    pub bonus_amount: f64,
    pub created_at: i64,
    pub updated_at: i64,
}

impl From<StorageBonusConfig> for BonusConfig {
    fn from(model: StorageBonusConfig) -> Self {
        BonusConfig {
            country_geoname_id: model.country_geoname_id as i32,
            country_name: model.country_name,
            bonus_amount: model.bonus_amount,
            created_at: model.created_at,
            updated_at: model.updated_at,
        }
    }
}

impl Into<StorageBonusConfig> for BonusConfig {
    fn into(self) -> StorageBonusConfig {
        StorageBonusConfig {
            country_geoname_id: self.country_geoname_id as i64,
            country_name: self.country_name,
            bonus_amount: self.bonus_amount,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
}