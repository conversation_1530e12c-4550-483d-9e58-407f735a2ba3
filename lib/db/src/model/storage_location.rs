use dpn_core::types::location::Location;


#[derive(Debug, serde::Serialize, sqlx::FromRow)]
pub struct LocationCountry {
    pub continent_code: String,
    pub continent_name: String,
    pub country_geoname_id: i32,
    pub country_iso_code: String,
    pub country_name: String,
}

#[derive(Debug, Clone, sqlx::FromRow)]
pub struct StorageLocation {
    pub geoname_id: i32,
    pub city_name: String,
    pub continent_geoname_id: i32,
    pub continent_code: String,
    pub continent_name: String,
    pub country_geoname_id: i32,
    pub country_iso_code: String,
    pub country_name: String,
}


impl From<StorageLocation> for Location {
    fn from(model: StorageLocation) -> Self {
        Location {
            geoname_id: model.geoname_id,
            city_name: model.city_name,
            continent_geoname_id: model.continent_geoname_id,
            continent_code: model.continent_code,
            continent_name: model.continent_name,
            country_geoname_id: model.country_geoname_id,
            country_iso_code: model.country_iso_code,
            country_name: model.country_name,
        }
    }
}

impl Into<StorageLocation> for Location {
    fn into(self) -> StorageLocation {
        StorageLocation {
            geoname_id: self.geoname_id,
            city_name: self.city_name,
            continent_geoname_id: self.continent_geoname_id,
            continent_code: self.continent_code,
            continent_name: self.continent_name,
            country_geoname_id: self.country_geoname_id,
            country_iso_code: self.country_iso_code,
            country_name: self.country_name,
        }
    }
}