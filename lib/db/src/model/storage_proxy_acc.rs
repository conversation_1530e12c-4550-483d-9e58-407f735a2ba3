use dpn_core::types::connection::{PrioritizedIPLevel, ProxyAccData};
use num_traits::FromPrimitive;

#[derive(Debug, <PERSON><PERSON>, sqlx::FromRow)]
pub struct StorageProxyAcc {
    pub id: String,
    pub password: String,
    pub ip_rotation_period: i64,
    pub whitelisted_ip: Option<String>,
    pub user_addr: String,
    pub country_geoname_id: i64,
    pub city_geoname_id: Option<i64>,
    pub rate_per_kb: i64,
    pub rate_per_second: i64,
    pub prioritized_ip: Option<String>,
    pub prioritized_ip_level: Option<i16>,
    pub created_at: i64,
}

impl From<StorageProxyAcc> for ProxyAccData {
    fn from(model: StorageProxyAcc) -> Self {
        ProxyAccData {
            id: model.id,
            password: model.password,
            ip_rotation_period: model.ip_rotation_period,
            whitelisted_ip: model.whitelisted_ip,
            user_addr: model.user_addr,
            country_geoname_id: model.country_geoname_id,
            city_geoname_id: model.city_geoname_id,
            rate_per_kb: model.rate_per_kb,
            rate_per_second: model.rate_per_second,
            prioritized_ip: model.prioritized_ip,
            prioritized_ip_level: model
                .prioritized_ip_level
                .map(|p| PrioritizedIPLevel::from_i16(p).unwrap()),
            created_at: model.created_at,
        }
    }
}

impl Into<StorageProxyAcc> for ProxyAccData {
    fn into(self) -> StorageProxyAcc {
        StorageProxyAcc {
            id: self.id,
            password: self.password,
            ip_rotation_period: self.ip_rotation_period,
            whitelisted_ip: self.whitelisted_ip,
            user_addr: self.user_addr,
            country_geoname_id: self.country_geoname_id,
            city_geoname_id: self.city_geoname_id,
            rate_per_kb: self.rate_per_kb,
            rate_per_second: self.rate_per_second,
            prioritized_ip: self.prioritized_ip,
            prioritized_ip_level: self.prioritized_ip_level.map(|p| p as i16),
            created_at: self.created_at,
        }
    }
}
