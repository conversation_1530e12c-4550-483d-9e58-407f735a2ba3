use dpn_core::types::referral::Referral;

use dpn_core::utils::bytes_to_hex_string;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, sqlx::FromRow)]
pub struct StorageReferral {
    pub user_addr: String,
    pub referral_code: Option<String>,
    pub created_at: i64,
    pub referred_by: Option<String>,
    pub referred_at: Option<i64>,
    pub tx_hash: Option<String>,
}

impl From<StorageReferral> for Referral {
    fn from(model: StorageReferral) -> Self {
        Referral {
            referral_code: model.referral_code,
            user_addr: model.user_addr.parse().unwrap(),
            created_at: model.created_at,
            referred_by: model.referred_by.map(|a| a.parse().unwrap()),
            referred_at: model.referred_at,
            tx_hash: model.tx_hash.map(|h| h.parse().unwrap()),
        }
    }
}

impl Into<StorageReferral> for Referral {
    fn into(self) -> StorageReferral {
        StorageReferral {
            referral_code: self.referral_code,
            user_addr: bytes_to_hex_string(self.user_addr.as_bytes()),
            created_at: self.created_at,
            referred_by: self.referred_by.map(|a| bytes_to_hex_string(a.as_bytes())),
            referred_at: self.referred_at,
            tx_hash: self.tx_hash.map(|h| bytes_to_hex_string(h.as_bytes())),
        }
    }
}
