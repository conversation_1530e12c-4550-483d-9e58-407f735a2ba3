use dpn_core::types::referral::ReferralsOverview;
use num_traits::ToPrimitive as _;
use sqlx::types::BigDecimal;

#[derive(Debug, sqlx::FromRow)]
pub struct StorageReferralsOverview {
    pub total_referees: Option<i64>,
    // number of commission transactions from referees
    pub total_commission_txs: Option<i64>,
    // commision earned from referees
    pub total_commision: Option<BigDecimal>,
    // unclaimed commision earned from referees
    pub unclaimed_commission: Option<BigDecimal>,
}

impl From<StorageReferralsOverview> for ReferralsOverview {
    fn from(model: StorageReferralsOverview) -> Self {
        ReferralsOverview {
            total_referees: model.total_referees.unwrap_or(0i64),
            total_commission_txs: model.total_commission_txs.unwrap_or(0i64),
            total_commision: model.total_commision.and_then(|x| x.to_i64()).unwrap_or(0),
            unclaimed_commission: model
                .unclaimed_commission
                .and_then(|x| x.to_i64())
                .unwrap_or(0),
        }
    }
}
