use dpn_core::types::region::UserRegionInfo;

#[derive(Debu<PERSON>, <PERSON><PERSON>, sqlx::FromRow)]
pub struct StorageUserRegionInfo {
    pub user_addr: String,
    pub city_geoname_id: u32,
    pub country_geoname_id: u32,
}

impl From<StorageUserRegionInfo> for UserRegionInfo {
    fn from(model: StorageUserRegionInfo) -> Self {
        UserRegionInfo {
            user_addr: model.user_addr,
            city_geoname_id: model.city_geoname_id,
            country_geoname_id: model.country_geoname_id,
        }
    }
}

impl Into<StorageUserRegionInfo> for UserRegionInfo {
    fn into(self) -> StorageUserRegionInfo {
        StorageUserRegionInfo {
            user_addr: self.user_addr,
            city_geoname_id: self.city_geoname_id,
            country_geoname_id: self.country_geoname_id,
        }
    }
}
