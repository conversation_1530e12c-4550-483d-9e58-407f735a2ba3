use dpn_core::types::region::UserRegionInfoHistory;

#[derive(Debu<PERSON>, Clone)]
pub struct StorageReionInfoHistory {
    pub geoname_id: i64,
    pub is_country: bool,
    pub name: String,
    pub country_geoname_id: Option<i64>,
    pub country_geoname_name: Option<String>
}

impl From<StorageReionInfoHistory> for UserRegionInfoHistory {
    fn from(model: StorageReionInfoHistory) -> Self {
        UserRegionInfoHistory {
            geoname_id: model.geoname_id,
            is_country: model.is_country,
            name: model.name,
            country_geoname_id: model.country_geoname_id,
            country_geoname_name: model.country_geoname_name,
        }
    }
}

impl Into<StorageReionInfoHistory> for UserRegionInfoHistory {
    fn into(self) -> StorageReionInfoHistory {
        StorageReionInfoHistory {
            geoname_id: self.geoname_id,
            is_country: self.is_country,
            name: self.name,
            country_geoname_id: self.country_geoname_id,
            country_geoname_name: self.country_geoname_name,
        }
    }
}
