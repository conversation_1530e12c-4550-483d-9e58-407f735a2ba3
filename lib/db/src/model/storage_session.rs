use dpn_core::{
    types::bandwidth::{Session, SessionStatus, SessionTerminationReason},
    utils::bytes_to_hex_string,
};
use num_traits::FromPrimitive;

use dpn_core::utils::{szabo_to_u256, u256_to_szabo};

#[derive(Debug, <PERSON>lone, sqlx::FromRow)]
pub struct StorageSession {
    pub session_hash: String,
    pub client_identifier: String,
    pub provider_addr: String,
    pub client_addr: String,
    pub rate_per_kb_v2: Option<i64>,
    pub rate_per_kb: Option<i64>,
    pub handshake_at: Option<i64>,
    pub end_at: Option<i64>,
    pub duration: Option<i64>,
    pub bandwidth_usage: Option<i64>,
    pub duration_fee: Option<i64>,
    pub bandwidth_fee: Option<i64>,
    pub total_fee: Option<i64>,
    pub total_fee_v2: Option<i64>, // this field refer for the total of u2u token 
    pub status: i32,
    pub reason: Option<i32>,
    pub tx_hash: Option<String>,
    pub provider_country_id: Option<i64>,
}

impl From<StorageSession> for Session {
    fn from(model: StorageSession) -> Self {
        Session {
            session_hash: model.session_hash.parse().unwrap(),
            client_identifier: model.client_identifier,
            provider_addr: model.provider_addr.parse().unwrap(),
            client_addr: model.client_addr.parse().unwrap(),
            rate_per_kb_v2: szabo_to_u256(model.rate_per_kb_v2.unwrap_or_default()),
            rate_per_kb: szabo_to_u256(model.rate_per_kb.unwrap_or_default()),
            handshake_at: model.handshake_at,
            end_at: model.end_at,
            duration: model.duration,
            bandwidth_usage: model.bandwidth_usage,
            duration_fee: szabo_to_u256(model.duration_fee.unwrap_or_default()),
            bandwidth_fee: szabo_to_u256(model.bandwidth_fee.unwrap_or_default()),
            total_fee: szabo_to_u256(model.total_fee.unwrap_or_default()),
            total_fee_v2: Some(szabo_to_u256(model.total_fee_v2.unwrap_or_default())), // this field refer for the total of u2u token 
            status: SessionStatus::from_i32(model.status).unwrap(),
            reason: model
                .reason
                .map(|r| SessionTerminationReason::from_i32(r).unwrap()),
            tx_hash: (match model.tx_hash {
                Some(h) => {
                    if h == "" {
                        None
                    } else {
                        Some(h)
                    }
                }
                None => None,
            })
            .map(|h| h.parse().unwrap()),
            provider_country_id: model.provider_country_id,
        }
    }
}

impl Into<StorageSession> for Session {
    fn into(self) -> StorageSession {
        StorageSession {
            session_hash: bytes_to_hex_string(self.session_hash.as_bytes()),
            client_identifier: self.client_identifier,
            provider_addr: bytes_to_hex_string(self.provider_addr.as_bytes()),
            client_addr: bytes_to_hex_string(self.client_addr.as_bytes()),
            rate_per_kb_v2: Some(u256_to_szabo(self.rate_per_kb_v2.clone())),
            rate_per_kb: Some(u256_to_szabo(self.rate_per_kb.clone())),
            handshake_at: self.handshake_at,
            end_at: self.end_at,
            duration: self.duration,
            bandwidth_usage: self.bandwidth_usage,
            duration_fee: Some(u256_to_szabo(self.duration_fee.clone())),
            bandwidth_fee: Some(u256_to_szabo(self.bandwidth_fee.clone())),
            total_fee: Some(u256_to_szabo(self.total_fee.clone())),
            total_fee_v2: Some(u256_to_szabo(self.total_fee_v2.clone().unwrap())), // this field refer for the total of u2u token 
            status: self.status as i32,
            reason: self.reason.map(|r| r as i32),
            tx_hash: self.tx_hash.map(|h| bytes_to_hex_string(h.as_bytes())),
            provider_country_id: self.provider_country_id,
        }
    }
}
