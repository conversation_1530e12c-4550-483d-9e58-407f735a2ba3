use dpn_core::types::tier::{Tier, UserTier};

const EQ_10_GB: i64 = 10_000_000; // 10 GB
const EQ_100_GB: i64 = 100_000_000; // 100 GB
const EQ_1_TB: i64 = 1_000_000_000; // 1 TB
const EQ_10_TB: i64 = 10_000_000_000; // 10 TB
const LT_10_GB: i64 = EQ_10_GB - 1;
const LT_100_GB: i64 = EQ_100_GB - 1;
const LT_1_TB: i64 = EQ_1_TB - 1;
const LT_10_TB: i64 = EQ_10_TB - 1;

#[derive(Debug, Clone, sqlx::FromRow)]
pub struct StorageUserTier {
    pub user_addr: String,
    pub points: i64,
}

impl From<StorageUserTier> for UserTier {
    fn from(model: StorageUserTier) -> Self {
        UserTier {
            user_addr: model.user_addr,
            tier: match model.points {
                ..=LT_10_GB => Tier::Bronze,
                EQ_10_GB..=LT_100_GB => Tier::Silver,
                EQ_100_GB..=LT_1_TB => Tier::Gold,
                EQ_1_TB..=LT_10_TB => Tier::Platinum,
                EQ_10_TB.. => Tier::Diamond,
            },
            points: model.points,
        }
    }
}

impl Into<StorageUserTier> for UserTier {
    fn into(self) -> StorageUserTier {
        StorageUserTier {
            user_addr: self.user_addr,
            points: self.points,
        }
    }
}
