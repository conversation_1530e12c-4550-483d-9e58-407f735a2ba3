use dpn_core::types::tier::TierPoint;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, sqlx::FromRow)]
pub struct StorageTierPoint {
    pub user_addr: String,
    pub points: i64,
    pub created_at: i64,
}

impl From<StorageTierPoint> for TierPoint {
    fn from(model: StorageTierPoint) -> Self {
        TierPoint {
            user_addr: model.user_addr,
            points: model.points,
            created_at: model.created_at,
        }
    }
}
