use dpn_core::types::tx::{Tx, TxStatus, TxType};
use num_traits::FromPrimitive;

use dpn_core::utils::{bytes_to_hex_string, szabo_to_u256, u256_to_szabo};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, sqlx::FromRow)]
pub struct StorageTx {
    pub tx_hash: String,
    pub from_addr: String,
    pub to_addr: String,
    /// In Szabo unit. Example: 6868.123456 U2U is converted to 6868123456
    pub amount: i64,
    pub tx_type: i32,
    pub tx_status: i32,
    pub chain_tx_hash: Option<String>,
    pub created_at: i64,
}

impl From<StorageTx> for Tx {
    fn from(model: StorageTx) -> Self {
        Tx {
            tx_hash: model.tx_hash.parse().unwrap(),
            from_addr: model.from_addr.parse().unwrap(),
            to_addr: model.to_addr.parse().unwrap(),
            amount: szabo_to_u256(model.amount),
            tx_type: TxType::from_i32(model.tx_type).unwrap(),
            tx_status: TxStatus::from_i32(model.tx_status).unwrap(),
            chain_tx_hash: model.chain_tx_hash.map(|txh| txh.parse().unwrap()),
            created_at: model.created_at,
        }
    }
}

impl Into<StorageTx> for Tx {
    fn into(self) -> StorageTx {
        StorageTx {
            tx_hash: bytes_to_hex_string(self.tx_hash.as_bytes()),
            from_addr: bytes_to_hex_string(self.from_addr.as_bytes()),
            to_addr: bytes_to_hex_string(self.to_addr.as_bytes()),
            amount: u256_to_szabo(self.amount.clone()),
            tx_type: self.tx_type as i32,
            tx_status: self.tx_status as i32,
            chain_tx_hash: self
                .chain_tx_hash
                .map(|x| bytes_to_hex_string(x.as_bytes())),
            created_at: self.created_at,
        }
    }
}
