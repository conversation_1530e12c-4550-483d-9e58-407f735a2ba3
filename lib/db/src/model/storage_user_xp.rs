use dpn_core::{types::user_xp::UserXp, utils::bytes_to_hex_string};


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, sqlx::FromRow)]
pub struct StorageUserXp {
    pub user_addr: String,
    pub minutes_uptime: f64,
    pub created_at: i64,
    pub updated_at: i64,
}

impl From<StorageUserXp> for UserXp {
    fn from(model: StorageUserXp) -> Self {
        UserXp {
            user_addr: model.user_addr.parse().unwrap(),
            minutes_uptime: model.minutes_uptime,
            created_at: model.created_at,
            updated_at: model.updated_at,
        }
    }
}

impl Into<StorageUserXp> for UserXp {
    fn into(self) -> StorageUserXp {
        StorageUserXp {
            user_addr: bytes_to_hex_string(self.user_addr.as_bytes()),
            minutes_uptime: self.minutes_uptime,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
}

