use anyhow::{anyhow, Error};
use dpn_core::types::connection::{PrioritizedIPLevel, ProxyAccData};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use crate::{model::storage_proxy_acc::StorageProxyAcc, StorageProcessor};
use bigdecimal::ToPrimitive;
use num_traits::FromPrimitive;



#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, sqlx::FromRow)]
pub struct ProxyAccDto {
    pub user_addr: String,
    pub username: String,
    pub passwd: String,
    pub ip_rotation_period: i16,
    pub whitelisted_ip: String,
    pub country_geoname_id: i64,
    pub rate_per_kb: i64,
    pub rate_per_second: i64,
    pub prioritized_ip: Option<String>,
    pub prioritized_ip_level: Option<PrioritizedIPLevel>,
    pub bandwidth: i64,
}

#[derive(Debug)]
pub struct ProxyAccDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl ProxyAccDal<'_, '_> {
    pub async fn create_proxy_acc(
        &mut self,
        proxy_acc: ProxyAccData,
    ) -> anyhow::Result<StorageProxyAcc> {
        let rec = sqlx::query!(
            r#"
                INSERT INTO proxy_accounts (
                    id,
                    passwd,
                    ip_rotation_period,
                    whitelisted_ip,
                    user_addr,
                    rate_per_kb,
                    rate_per_second,
                    country_geoname_id,
                    prioritized_ip,
                    prioritized_ip_level,
                    created_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                 ON CONFLICT (id) DO UPDATE SET
                    passwd = EXCLUDED.passwd,
                    ip_rotation_period = EXCLUDED.ip_rotation_period,
                    whitelisted_ip = EXCLUDED.whitelisted_ip,
                    user_addr = EXCLUDED.user_addr,
                    rate_per_kb = EXCLUDED.rate_per_kb,
                    rate_per_second = EXCLUDED.rate_per_second,
                    country_geoname_id = EXCLUDED.country_geoname_id,
                    prioritized_ip = EXCLUDED.prioritized_ip,
                    prioritized_ip_level = EXCLUDED.prioritized_ip_level
                RETURNING *;
            "#,
            proxy_acc.id,
            proxy_acc.password,
            proxy_acc.ip_rotation_period,
            proxy_acc.whitelisted_ip,
            proxy_acc.user_addr,
            proxy_acc.rate_per_kb,
            proxy_acc.rate_per_second,
            proxy_acc.country_geoname_id,
            proxy_acc.prioritized_ip,
            proxy_acc.prioritized_ip_level.map(|lvl| lvl as i16),
            proxy_acc.created_at
        )
        .fetch_one(self.storage.conn())
        .await;

        match rec {
            Ok(rec) => Ok(StorageProxyAcc {
                id: rec.id,
                password: rec.passwd,
                ip_rotation_period: rec.ip_rotation_period,
                whitelisted_ip: rec.whitelisted_ip,
                user_addr: rec.user_addr,
                country_geoname_id: rec.country_geoname_id,
                city_geoname_id: rec.city_geoname_id,
                rate_per_kb: rec.rate_per_kb,
                rate_per_second: rec.rate_per_second,
                prioritized_ip: rec.prioritized_ip,
                prioritized_ip_level: rec.prioritized_ip_level,
                created_at: rec.created_at,
            }),
            Err(e) => Err(anyhow!(
                "Failed to execute insert proxy account query, err={}",
                e
            )),
        }
    }

    pub async fn get_proxy_accounts_by_user_addr(
        &mut self,
        user_addr: String,
    ) -> anyhow::Result<Vec<ProxyAccDto>> {
        match sqlx::query!(
            r#"
                WITH session_served as (
                    SELECT client_identifier, bandwidth_usage
                    FROM sessions
                    WHERE client_addr = $1
                )
                SELECT pa.user_addr as user_addr, pa.id as username, pa.passwd as passwd,
                pa.whitelisted_ip as whitelisted_ip,
                pa.ip_rotation_period as ip_rotation_period,
                pa.country_geoname_id as country_geoname_id,
                pa.rate_per_kb as rate_per_kb, pa.rate_per_second as rate_per_second,
                pa.prioritized_ip as prioritized_ip, pa.prioritized_ip_level as prioritized_ip_level,
                SUM(s.bandwidth_usage) as bandwidth
                FROM proxy_accounts pa
                LEFT JOIN session_served s ON pa.id = s.client_identifier
                WHERE pa.user_addr = $1
                GROUP BY user_addr, username, passwd, whitelisted_ip, ip_rotation_period,
                country_geoname_id, rate_per_kb, rate_per_second,
                prioritized_ip, prioritized_ip_level
            "#,
            user_addr,
        )
        .fetch_all(self.storage.conn())
        .await
        {
            Ok(rs) => Ok(rs
                .iter()
                .map(|rec| ProxyAccDto{
                    user_addr: rec.user_addr.clone(),
                    username: rec.username.clone(),
                    passwd: rec.passwd.clone(),
                    ip_rotation_period: rec.ip_rotation_period as i16,
                    whitelisted_ip: rec.whitelisted_ip.clone().unwrap_or("".to_string()),
                    country_geoname_id: rec.country_geoname_id,
                    rate_per_kb: rec.rate_per_kb,
                    rate_per_second: rec.rate_per_second,
                    prioritized_ip: rec.prioritized_ip.clone(),
                    prioritized_ip_level: rec
                        .prioritized_ip_level
                        .clone()
                        .map(|p| PrioritizedIPLevel::from_i16(p).unwrap()),
                    bandwidth: rec.bandwidth.clone().and_then(|bd| {
                        bd.to_i64()
                    }).unwrap_or_default()

                })
                .collect()),
            Err(e) => {
                return Err(anyhow!(
                    "Failed to execute select proxy accounts of user_addr={} query, err={}",
                    user_addr,
                    e
                ))
            }
        }
    }

    pub async fn get_all_proxy_accounts(&mut self) -> anyhow::Result<Vec<StorageProxyAcc>> {
        match sqlx::query!(
            r#"
                SELECT * FROM proxy_accounts
            "#,
        )
        .fetch_all(self.storage.conn())
        .await
        {
            Ok(rs) => Ok(rs
                .iter()
                .map(|rec| StorageProxyAcc {
                    id: rec.id.clone(),
                    password: rec.passwd.clone(),
                    ip_rotation_period: rec.ip_rotation_period,
                    whitelisted_ip: rec.whitelisted_ip.clone(),
                    user_addr: rec.user_addr.clone(),
                    country_geoname_id: rec.country_geoname_id,
                    city_geoname_id: rec.city_geoname_id,
                    rate_per_kb: rec.rate_per_kb,
                    rate_per_second: rec.rate_per_second,
                    prioritized_ip: rec.prioritized_ip.clone(),
                    prioritized_ip_level: rec.prioritized_ip_level,
                    created_at: rec.created_at,
                })
                .collect()),
            Err(e) => {
                return Err(anyhow!(
                    "Failed to execute select proxy accounts query, err={}",
                    e
                ))
            }
        }
    }

    pub async fn get_proxy_acc_by_id(
        &mut self,
        proxy_acc_id: String,
    ) -> anyhow::Result<StorageProxyAcc> {
        let rec = sqlx::query!(
            r#"
                SELECT *
                FROM proxy_accounts
                WHERE id = $1;
            "#,
            proxy_acc_id,
        )
        .fetch_one(self.storage.conn())
        .await;

        match rec {
            Ok(rec) => Ok(StorageProxyAcc {
                id: rec.id,
                password: rec.passwd,
                ip_rotation_period: rec.ip_rotation_period,
                whitelisted_ip: rec.whitelisted_ip,
                user_addr: rec.user_addr,
                country_geoname_id: rec.country_geoname_id,
                city_geoname_id: rec.city_geoname_id,
                rate_per_kb: rec.rate_per_kb,
                rate_per_second: rec.rate_per_second,
                prioritized_ip: rec.prioritized_ip,
                prioritized_ip_level: rec.prioritized_ip_level,
                created_at: rec.created_at,
            }),
            Err(e) => Err(anyhow!("Failed to execute query, err={}", e)),
        }
    }

    pub async fn get_proxy_acc_by_ip(&mut self, ip: String) -> anyhow::Result<StorageProxyAcc> {
        let rec = sqlx::query!(
            r#"
                SELECT *
                FROM proxy_accounts
                WHERE whitelisted_ip = $1;
            "#,
            ip,
        )
        .fetch_one(self.storage.conn())
        .await;

        match rec {
            Ok(rec) => Ok(StorageProxyAcc {
                id: rec.id,
                password: rec.passwd,
                ip_rotation_period: rec.ip_rotation_period,
                whitelisted_ip: rec.whitelisted_ip,
                user_addr: rec.user_addr,
                country_geoname_id: rec.country_geoname_id,
                city_geoname_id: rec.city_geoname_id,
                rate_per_kb: rec.rate_per_kb,
                rate_per_second: rec.rate_per_second,
                prioritized_ip: rec.prioritized_ip,
                prioritized_ip_level: rec.prioritized_ip_level,
                created_at: rec.created_at,
            }),
            Err(e) => Err(anyhow!("Failed to execute query, err={}", e)),
        }
    }

    pub async fn update_proxy_account(
        &mut self,
        user_addr: String,
        proxy_acc_id: String,
        ip_rotation_period: i64,
        whitelisted_ip: Option<String>,
        rate_per_kb: i64,
        rate_per_second: i64,
        country_geoname_id: i64,
        city_geoname_id: Option<i64>,
        prioritized_ip: Option<String>,
        prioritized_ip_level: Option<PrioritizedIPLevel>,
    ) -> anyhow::Result<StorageProxyAcc> {
        let rec = sqlx::query!(
            r#"
                UPDATE proxy_accounts
                SET ip_rotation_period = $3, 
                    whitelisted_ip = $4,
                    rate_per_kb = $5, 
                    rate_per_second = $6, 
                    country_geoname_id = $7,
                    city_geoname_id = $8,
                    prioritized_ip = $9,
                    prioritized_ip_level = $10
                WHERE id = $1 AND user_addr = $2
                RETURNING *
            "#,
            proxy_acc_id,
            user_addr,
            ip_rotation_period,
            whitelisted_ip,
            rate_per_kb,
            rate_per_second,
            country_geoname_id,
            city_geoname_id,
            prioritized_ip,
            prioritized_ip_level.map(|p| p as i16)
        )
        .fetch_one(self.storage.conn())
        .await
        .map_err(|e| anyhow!("failed to update proxy acc err={}", e))?;

        Ok(StorageProxyAcc {
            id: rec.id,
            password: rec.passwd,
            ip_rotation_period: rec.ip_rotation_period,
            whitelisted_ip: rec.whitelisted_ip,
            user_addr: rec.user_addr,
            country_geoname_id: rec.country_geoname_id,
            city_geoname_id: rec.city_geoname_id,
            rate_per_kb: rec.rate_per_kb,
            rate_per_second: rec.rate_per_second,
            prioritized_ip: rec.prioritized_ip,
            prioritized_ip_level: rec.prioritized_ip_level,
            created_at: rec.created_at,
        })
    }

    pub async fn delete_proxy_account(
        &mut self,
        proxy_acc_id: String,
        user_addr: String,
    ) -> anyhow::Result<()> {
        match sqlx::query!(
            r#"
                DELETE FROM proxy_accounts  
                WHERE id = $1 AND user_addr = $2
            "#,
            proxy_acc_id,
            user_addr
        )
        .execute(self.storage.conn())
        .await
        {
            Ok(rs) => match rs.rows_affected() {
                1 => {
                    return Ok(());
                }
                _ => {
                    return Err(Error::msg(format!(
                        "failed to execute query delete proxy account, 0 rows affected"
                    )));
                }
            },
            Err(e) => {
                return Err(Error::msg(format!(
                    "failed to delete proxy account, err={}",
                    e
                )));
            }
        }
    }

    pub async fn count_proxy_acc_by_addr(&mut self, user_addr: String) -> anyhow::Result<Option<i64>>{
        match sqlx::query!(
            r#"
                SELECT COUNT(*) as count 
                FROM proxy_accounts
                WHERE user_addr = $1
            "#,
            user_addr,
        )
        .fetch_one(self.storage.conn())
        .await
        {
            Ok(rec) => Ok(rec.count),
            Err(e) => Err(anyhow!(
                "Failed to execute count proxy accounts query, err={}",
                e
            )),
        }
    }
}
