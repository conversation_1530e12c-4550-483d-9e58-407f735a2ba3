use anyhow::{anyhow, Context, Error};
use dpn_core::types::{
    internal_tx::InternalTxType,
    tx::{TxStatus, TxType},
};
use sqlx::types::chrono::Utc;

use crate::{
    model::{
        storage_referral::StorageReferral, storage_referrals_overview::StorageReferralsOverview,
    },
    StorageProcessor,
};

#[derive(Debug)]
pub struct ReferralDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl ReferralDal<'_, '_> {
    pub async fn create_empty_referral(&mut self, user_addr: String) -> anyhow::Result<()> {
        return match sqlx::query!(
            r#"
                INSERT INTO referrals (user_addr, created_at)
                VALUES ($1, $2)
                ON CONFLICT (user_addr) DO NOTHING;
            "#,
            user_addr,
            Utc::now().timestamp(),
        )
        .execute(self.storage.conn())
        .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow!(
                "Failed to execute create empty referral query, err={}",
                e
            )),
        };
    }

    pub async fn create_referral_code(
        &mut self,
        referral_code: String,
        user_addr: String,
    ) -> anyhow::Result<()> {
        let mut transaction = self
            .storage
            .start_transaction()
            .await
            .context("start_transaction()")?;

        _ = sqlx::query!(
            r#"
                INSERT INTO referrals (user_addr, created_at)
                VALUES ($1, $2)
                ON CONFLICT (user_addr) DO NOTHING;
            "#,
            user_addr,
            Utc::now().timestamp(),
        )
        .execute(transaction.conn())
        .await;

        match sqlx::query!(
            r#"
                SELECT * FROM referrals
                WHERE referral_code = $1;
            "#,
            referral_code,
        )
        .fetch_optional(transaction.conn())
        .await
        {
            Ok(None) => {
                match sqlx::query!(
                    r#"
                        UPDATE referrals
                        SET referral_code = $2
                        WHERE user_addr = $1;
                    "#,
                    user_addr,
                    referral_code,
                )
                .execute(transaction.conn())
                .await
                {
                    Ok(rs) => match rs.rows_affected() {
                        1 => {
                            transaction.commit().await.context("commit()")?;
                            return Ok(());
                        }
                        _ => {
                            transaction.commit().await.context("rollback()")?;
                            return Err(Error::msg(format!("Database is not working correctly")));
                        }
                    },
                    Err(e) => {
                        transaction.commit().await.context("rollback()")?;
                        return Err(Error::msg(format!(
                            "Failed to update referral code err={}",
                            e
                        )));
                    }
                }
            }
            Ok(Some(_)) => {
                return Err(Error::msg(format!(
                    "Failed to create referral code, {} already used",
                    referral_code
                )));
            }
            Err(e) => {
                return Err(Error::msg(format!(
                    "Failed when check referral_code is used err={}",
                    e
                )));
            }
        }
    }

    pub async fn import_referral_code(
        &mut self,
        user_addr: String,
        referral_code: String,
    ) -> anyhow::Result<(String, String)> {
        let mut transaction = self
            .storage
            .start_transaction()
            .await
            .context("start_transaction()")?;

        let rec = sqlx::query!(
            r#"
                SELECT user_addr, referred_by
                FROM referrals
                WHERE referral_code = $1
            "#,
            referral_code,
        )
        .fetch_one(transaction.conn())
        .await;
        if let Err(_) = rec {
            return Err(Error::msg(format!("referral code not found")));
        }
        let rec_rs = rec.unwrap();
        let referred_by = rec_rs.referred_by;
        let referrer_addr = rec_rs.user_addr;

        if let Some(_referred_by_f0) = referred_by {
            if _referred_by_f0 == user_addr {
                return Err(Error::msg(format!("Can not to enter your referred user")));
            }
        }

        _ = sqlx::query!(
            r#"
                INSERT INTO referrals (user_addr, created_at)
                VALUES ($1, $2)
                ON CONFLICT (user_addr) DO NOTHING;
            "#,
            user_addr,
            Utc::now().timestamp(),
        )
        .execute(transaction.conn())
        .await?;

        match sqlx::query!(
            r#"
                SELECT * FROM referrals
                WHERE user_addr = $1 AND referred_by IS NULL;
            "#,
            user_addr,
        )
        .fetch_optional(transaction.conn())
        .await
        {
            Ok(Some(self_ref)) => {
                if let Some(self_referral_code) = self_ref.referral_code.clone() {
                    if self_referral_code == referral_code {
                        return Err(Error::msg(format!("Can not enter your referral code")));
                    }
                }
                match sqlx::query!(
                    r#"
                        UPDATE referrals
                        SET referred_by = $2, referred_at = $3
                        WHERE user_addr = $1;
                    "#,
                    user_addr,
                    referrer_addr,
                    Utc::now().timestamp(),
                )
                .execute(transaction.conn())
                .await
                {
                    Ok(rs) => match rs.rows_affected() {
                        1 => {
                            transaction.commit().await.context("commit()")?;
                            return Ok((user_addr, referrer_addr));
                        }
                        _ => {
                            transaction.commit().await.context("rollback()")?;
                            return Err(Error::msg(format!("database is not working correctly")));
                        }
                    },
                    Err(e) => {
                        transaction.commit().await.context("rollback()")?;
                        return Err(Error::msg(format!("failed to link referral, error when run query update referral code err={}", e)));
                    }
                }
            }
            Ok(None) => {
                transaction.commit().await.context("rollback()")?;
                return Err(Error::msg(format!(
                    "user_addr {} already enter referral code",
                    user_addr
                )));
            }
            Err(e) => {
                transaction.commit().await.context("rollback()")?;
                return Err(Error::msg(format!(
                    "failed to get referral of user {} with error: {}",
                    user_addr, e
                )));
            }
        }
    }

    pub async fn get_referrer_addr_by_referral_code(
        &mut self,
        referral_code: String,
    ) -> anyhow::Result<String> {
        let rec = sqlx::query!(
            r#"
                SELECT user_addr
                FROM referrals
                WHERE referral_code = $1
            "#,
            referral_code,
        )
        .fetch_one(self.storage.conn())
        .await;
    
        if let Err(_) = rec {
            return Err(Error::msg(format!("referral code not found")));
        }

        match rec {
            Ok(rec) => Ok(rec.user_addr),
            Err(e) => Err(anyhow!(
                "Failed to get referrer address by referral code: {}, err={}",
                referral_code,
                e
            )),
        }

    }
    pub async fn get_referral_of(&mut self, user_addr: String) -> anyhow::Result<StorageReferral> {
        let record = sqlx::query!(
            r#"
                SELECT * 
                FROM referrals 
                WHERE user_addr = $1
            "#,
            user_addr
        )
        .fetch_optional(self.storage.conn())
        .await?;

        match record {
            Some(rec) => Ok(StorageReferral {
                referral_code: rec.referral_code,
                user_addr: rec.user_addr,
                created_at: rec.created_at,
                referred_by: rec.referred_by,
                referred_at: rec.referred_at,
                tx_hash: rec.tx_hash,
            }),
            None => Err(anyhow::anyhow!(
                "Referral of user address = {} not found",
                user_addr
            )),
        }
    }

    pub async fn get_referrals_by_addr(
        &mut self,
        user_addr: String,
    ) -> anyhow::Result<Vec<StorageReferral>> {
        let records = sqlx::query!(
            r#"
                SELECT * 
                FROM referrals 
                WHERE referred_by = $1
                ORDER BY referred_at DESC
                LIMIT 20;
            "#,
            user_addr
        )
        .fetch_all(self.storage.conn())
        .await?;

        let mut result = Vec::new();

        for rec in records {
            result.push(StorageReferral {
                referral_code: rec.referral_code,
                user_addr: rec.user_addr,
                created_at: rec.created_at,
                referred_by: rec.referred_by,
                referred_at: rec.referred_at,
                tx_hash: rec.tx_hash,
            });
        }

        Ok(result)
    }

    pub async fn get_referrals_overview_of(
        &mut self,
        user_addr: String,
    ) -> anyhow::Result<StorageReferralsOverview> {
        let rec = sqlx::query!(
            r#" 
                SELECT
                    (SELECT count(*) FROM referrals 
                        WHERE referred_by = $1) AS total_referees,
                    (SELECT COUNT(*) FROM internal_transactions
                        WHERE to_addr = $1 
                        AND tx_type = $3 
                        AND tx_status = $4
                    ) AS total_commission_txs,
                    (   
                        SELECT SUM(amount) as rewards_amount
                        FROM internal_transactions
                        WHERE to_addr = $1 AND
                            tx_type = $3 AND 
                            tx_status = $4
                    ) AS total_commision,
                    (
                        WITH LastSuccessfulWithdrawalTransaction AS (
                            SELECT COALESCE(MAX(created_at), 0) AS last_successful_withdrawal_timestamp
                            FROM transactions
                            WHERE from_addr = $1 AND 
                                tx_type = $2 AND 
                                tx_status = $4
                        )
                        SELECT SUM(amount) as rewards_amount
                        FROM internal_transactions
                        WHERE to_addr = $1 AND
                            tx_type = $3 AND
                            tx_status = $4 AND
                            created_at >= (SELECT last_successful_withdrawal_timestamp 
                                            FROM LastSuccessfulWithdrawalTransaction)
                    ) AS unclaimed_commission
            "#,
            user_addr,
            TxType::Withdrawal as i32,
            InternalTxType::Commission as i32,
            TxStatus::Success  as i32,
        )
        .fetch_one(self.storage.conn())
        .await;

        match rec {
            Ok(rec) => Ok(StorageReferralsOverview {
                total_referees: rec.total_referees,
                total_commission_txs: rec.total_commission_txs,
                total_commision: rec.total_commision,
                unclaimed_commission: rec.unclaimed_commission,
            }
            .into()),
            Err(e) => Err(anyhow!(
                "Failed to execute get referrals overview of user address: {} query, err={}",
                user_addr,
                e
            )),
        }
    }
}
