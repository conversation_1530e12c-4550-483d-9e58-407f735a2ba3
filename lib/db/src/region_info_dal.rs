use anyhow::{Context, Error};

use crate::StorageProcessor;

#[derive(Debug)]
pub struct RegionInfoDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl RegionInfoDal<'_, '_> {
    pub async fn create_region_info(
        &mut self,
        user_addr: String,
        city_geoname_id: u32,
        country_geoname_id: u32,
    ) -> anyhow::Result<()> {
        let mut transaction = self
            .storage
            .start_transaction()
            .await
            .context("start_transaction()")?;

        match sqlx::query!(
            r#"
                INSERT INTO user_region_info (user_addr, city_geoname_id, country_geoname_id)
                VALUES ($1, $2, $3)
                ON CONFLICT (user_addr) DO NOTHING;
            "#,
            user_addr,
            city_geoname_id as i32,
            country_geoname_id as i32,
        )
        .execute(transaction.conn())
        .await
        {
            Ok(rs) => match rs.rows_affected() {
                1 => {
                    transaction.commit().await.context("commit()")?;
                    return Ok(());
                }
                _ => {
                    transaction.commit().await.context("rollback()")?;
                    return Err(Error::msg(format!("failed to create region info")));
                }
            },
            Err(e) => {
                transaction.commit().await.context("rollback()")?;
                return Err(Error::msg(format!(
                    "failed to execute insert user region info query, err = {}",
                    e
                )));
            }
        }
    }
}
