use anyhow::Error;

use crate::{model::storage_region_info_history::StorageReionInfoHistory, StorageProcessor};

#[derive(Debug)]
pub struct RegionInfoHistoryDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl RegionInfoHistoryDal<'_, '_> {
    pub async fn create_region_info_history(
        &mut self,
        region_id: i64,
        is_country: bool,
        name: String,
        country_geoname_id: Option<i64>,
        country_geoname_name: Option<String>
    ) -> anyhow::Result<()> {
        match sqlx::query!(
            r#"
                INSERT INTO region_info_history (geoname_id, is_country, name, country_geoname_id, country_geoname_name) 
                VALUES($1, $2, $3, $4, $5);
            "#,
            region_id,
            is_country,
            name,
            country_geoname_id,
            country_geoname_name
        )
        .execute(self.storage.conn())
        .await {
            Ok(_) => Ok(()),
            Err(e) => {
                return Err(Error::msg(format!(
                    "Failed to execute insert region info history, err = {}",
                    e
                )));
            },
        }
    }

    pub async fn get_all_region_info_history(
        &mut self,
    ) -> anyhow::Result<Vec<StorageReionInfoHistory>> {
        match sqlx::query!(
            r#"
                SELECT * FROM region_info_history
            "#,
        )
        .fetch_all(self.storage.conn())
        .await
        {
            Ok(rs) => Ok(rs
                .iter()
                .map(|rec| StorageReionInfoHistory {
                    geoname_id: rec.geoname_id,
                    is_country: rec.is_country,
                    name: rec.name.clone(),
                    country_geoname_id: rec.country_geoname_id,
                    country_geoname_name: rec.country_geoname_name.clone(),
                })
                .collect()),
            Err(e) => {
                return Err(Error::msg(format!(
                    "Failed to execute get all region info history, err={}",
                    e
                )))
            }
        }
    }
}
