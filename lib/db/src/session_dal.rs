use crate::{model::storage_session::StorageSession, StorageProcessor};
use dpn_core::types::{bandwidth::SessionStatus, internal_tx::InternalTxType, tx::TxStatus};
use num_traits::ToPrimitive;

use anyhow::Context as _;

#[derive(Debug)]
pub struct SessionDal<'a, 'c> {
    pub storage: &'a mut StorageProcessor<'c>,
}

impl SessionDal<'_, '_> {
    pub async fn create_session(
        &mut self,
        session_hash: String,
        client_identifier: String,
        provider_addr: String,
        client_addr: String,
        rate_per_kb_v2: i64,
        rate_per_kb: i64,
        handshake_at: i64,
        provider_country_id: i64,
    ) -> anyhow::Result<StorageSession> {
        let mut transaction = self
            .storage
            .start_transaction()
            .await
            .context("start_transaction()")?;

        let rec = sqlx::query!(
            r#"
                INSERT INTO sessions (session_hash, provider_addr, client_addr, status, rate_per_kb_v2, rate_per_kb, handshake_at, bandwidth_usage, duration, client_identifier, provider_country_id, total_fee_v2)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                ON CONFLICT (session_hash) DO NOTHING
                RETURNING *;
                "#,
            session_hash,
            provider_addr,
            client_addr,
            SessionStatus::Active as i32,
            rate_per_kb_v2,
            rate_per_kb,
            handshake_at,
            0,
            0,
            client_identifier,
            provider_country_id,
            0,
        )
        .fetch_one(transaction.conn())
        .await?;

        transaction.commit().await.context("commit()")?;
        Ok(StorageSession {
            session_hash: rec.session_hash,
            client_identifier: rec.client_identifier,
            provider_addr: rec.provider_addr,
            client_addr: rec.client_addr,
            rate_per_kb_v2: rec.rate_per_kb_v2,
            rate_per_kb: rec.rate_per_kb,
            handshake_at: rec.handshake_at,
            end_at: rec.end_at,
            duration: rec.duration,
            bandwidth_usage: rec.bandwidth_usage,
            duration_fee: rec.duration_fee,
            bandwidth_fee: rec.bandwidth_fee,
            total_fee: rec.total_fee,
            status: rec.status,
            reason: rec.reason,
            tx_hash: rec.tx_hash,
            provider_country_id: rec.provider_country_id,
            total_fee_v2: rec.total_fee_v2,
        })
    }

    pub async fn modify_session(
        &mut self,
        session_hash: String,
        status: i32,
        reason: Option<i32>,
        tx_hash: Option<String>,
        end_at: Option<i64>,
        duration: Option<i64>,
        bandwidth_usage: Option<i64>,
        duration_fee: Option<i64>,
        bandwidth_fee: Option<i64>,
        total_fee: Option<i64>,
        provider_country_id: Option<i64>,
        total_fee_v2: Option<i64>,
    ) -> anyhow::Result<StorageSession> {
        let rec = sqlx::query!(
            r#"
                UPDATE sessions 
                SET 
                tx_hash = $2,
                end_at = $3,
                duration = $4,
                bandwidth_usage = $5,
                duration_fee = $6,
                bandwidth_fee = $7,
                total_fee = $8,
                status = $9,
                reason = $10,
                provider_country_id = $11,
                total_fee_v2 = $12
                WHERE session_hash = $1
                RETURNING *
            "#,
            session_hash,
            tx_hash,
            end_at,
            duration,
            bandwidth_usage,
            duration_fee,
            bandwidth_fee,
            total_fee,
            status,
            reason,
            provider_country_id,
            total_fee_v2,
        )
        .fetch_one(self.storage.conn())
        .await?;

        Ok(StorageSession {
            session_hash: rec.session_hash,
            client_identifier: rec.client_identifier,
            provider_addr: rec.provider_addr,
            client_addr: rec.client_addr,
            rate_per_kb_v2: rec.rate_per_kb_v2,
            rate_per_kb: rec.rate_per_kb,
            handshake_at: rec.handshake_at,
            end_at: rec.rate_per_kb,
            duration: rec.rate_per_kb,
            bandwidth_usage: rec.bandwidth_usage,
            duration_fee: rec.duration_fee,
            bandwidth_fee: rec.bandwidth_fee,
            total_fee: rec.total_fee,
            status: rec.status,
            reason: rec.reason,
            tx_hash: rec.tx_hash.clone(),
            provider_country_id: rec.provider_country_id,
            total_fee_v2: rec.total_fee_v2,
        })
    }

    pub async fn update_bandwidth_usage(
        &mut self,
        session_hash: String,
        bandwidth_usage: i64,
    ) -> anyhow::Result<StorageSession> {
        let rec = sqlx::query!(
            r#"
                UPDATE sessions 
                SET bandwidth_usage = bandwidth_usage + $2
                WHERE session_hash = $1
                RETURNING *;
            "#,
            session_hash,
            bandwidth_usage,
        )
        .fetch_one(self.storage.conn())
        .await?;

        Ok(StorageSession {
            session_hash: rec.session_hash,
            client_identifier: rec.client_identifier,
            provider_addr: rec.provider_addr,
            client_addr: rec.client_addr,
            rate_per_kb_v2: rec.rate_per_kb_v2,
            rate_per_kb: rec.rate_per_kb,
            handshake_at: rec.handshake_at,
            end_at: rec.end_at,
            duration: rec.duration,
            bandwidth_usage: rec.bandwidth_usage,
            duration_fee: rec.duration_fee,
            bandwidth_fee: rec.bandwidth_fee,
            total_fee: rec.total_fee,
            status: rec.status,
            reason: rec.reason,
            tx_hash: rec.tx_hash,
            provider_country_id: rec.provider_country_id,
            total_fee_v2: rec.total_fee_v2,
        })
    }

    pub async fn update_tx_hash(
        &mut self,
        session_hash: String,
        tx_hash: Option<String>,
    ) -> anyhow::Result<()> {
        sqlx::query!(
            r#"
                UPDATE sessions
                SET tx_hash = $2
                WHERE session_hash = $1;
            "#,
            session_hash,
            tx_hash,
        )
        .execute(self.storage.conn())
        .await?;

        Ok(())
    }

    pub async fn get_session_by_id(
        &mut self,
        session_hash: String,
    ) -> anyhow::Result<Option<StorageSession>> {
        let record = sqlx::query!(
            r#"
                SELECT * 
                FROM sessions 
                WHERE session_hash = $1
                LIMIT 1
            "#,
            session_hash
        )
        .fetch_optional(self.storage.conn())
        .await?;

        Ok(record.map(|rec| StorageSession {
            session_hash: rec.session_hash,
            client_identifier: rec.client_identifier,
            provider_addr: rec.provider_addr,
            client_addr: rec.client_addr,
            rate_per_kb_v2: rec.rate_per_kb_v2,
            rate_per_kb: rec.rate_per_kb,
            handshake_at: rec.handshake_at,
            end_at: rec.end_at,
            duration: rec.duration,
            bandwidth_usage: rec.bandwidth_usage,
            duration_fee: rec.duration_fee,
            bandwidth_fee: rec.bandwidth_fee,
            total_fee: rec.total_fee,
            total_fee_v2: rec.total_fee_v2,
            status: rec.status,
            reason: rec.reason,
            tx_hash: rec.tx_hash,
            provider_country_id: rec.provider_country_id,
        }))
    }

    pub async fn connection_overview(
        &mut self,
        provider_addr: String,
    ) -> anyhow::Result<(i64, i64, i64)> {
        let record = sqlx::query!(
            r#"
                WITH ProviderSessionStats AS (
                    SELECT 
                        COUNT(*) as total_sessions,
                        SUM(bandwidth_usage) as total_bandwidth_usages
                    FROM sessions
                    WHERE provider_addr = $1
                )
                SELECT 
                    (SELECT total_sessions FROM ProviderSessionStats) AS total_sessions,
                    (SELECT total_bandwidth_usages FROM ProviderSessionStats) AS total_bandwidth_usages,
                    (SELECT sum(amount) FROM internal_transactions
                        WHERE to_addr = $1 AND tx_status = $2 AND tx_type = $3) AS total_network_rewards
            "#,
            provider_addr,
            TxStatus::Success as i32,
            InternalTxType::Network as i32,
        )
        .fetch_one(self.storage.conn())
        .await?;

        Ok((
            record.total_sessions.unwrap_or(0),
            record
                .total_network_rewards
                .and_then(|x| x.into_bigint_and_exponent().0.to_i64())
                .unwrap_or(0),
            record
                .total_bandwidth_usages
                .and_then(|x| x.into_bigint_and_exponent().0.to_i64())
                .unwrap_or(0),
        ))
    }

    pub async fn get_active_sessions(&mut self) -> anyhow::Result<Vec<StorageSession>> {
        let record = sqlx::query!(
            r#"
                SELECT * 
                FROM sessions 
                WHERE status = $1
                ORDER BY handshake_at DESC
            "#,
            SessionStatus::Active as i32
        )
        .fetch_all(self.storage.conn())
        .await?;

        Ok(record
            .iter()
            .map(|rec| StorageSession {
                session_hash: rec.session_hash.clone(),
                client_identifier: rec.client_identifier.clone(),
                provider_addr: rec.provider_addr.clone(),
                client_addr: rec.client_addr.clone(),
                rate_per_kb_v2: rec.rate_per_kb_v2,
                rate_per_kb: rec.rate_per_kb,
                handshake_at: rec.handshake_at,
                end_at: rec.end_at,
                duration: rec.duration,
                bandwidth_usage: rec.bandwidth_usage,
                duration_fee: rec.duration_fee,
                bandwidth_fee: rec.bandwidth_fee,
                total_fee: rec.total_fee,
                status: rec.status,
                tx_hash: rec.tx_hash.clone(),
                reason: rec.reason,
                provider_country_id: rec.provider_country_id,
                total_fee_v2: rec.total_fee_v2,
            })
            .collect())
    }

    pub async fn get_active_sessions_by_provider(
        &mut self,
        provider_addr: String,
    ) -> anyhow::Result<Vec<StorageSession>> {
        let record = sqlx::query!(
            r#"
                SELECT * 
                FROM sessions 
                WHERE provider_addr = $1 AND status = $2
                ORDER BY handshake_at DESC
            "#,
            provider_addr,
            SessionStatus::Active as i32
        )
        .fetch_all(self.storage.conn())
        .await?;

        Ok(record
            .iter()
            .map(|rec| StorageSession {
                session_hash: rec.session_hash.clone(),
                client_identifier: rec.client_identifier.clone(),
                provider_addr: rec.provider_addr.clone(),
                client_addr: rec.client_addr.clone(),
                rate_per_kb_v2: rec.rate_per_kb_v2,
                rate_per_kb: rec.rate_per_kb,
                handshake_at: rec.handshake_at,
                end_at: rec.end_at,
                duration: rec.duration,
                bandwidth_usage: rec.bandwidth_usage,
                duration_fee: rec.duration_fee,
                bandwidth_fee: rec.bandwidth_fee,
                total_fee: rec.total_fee,
                status: rec.status,
                tx_hash: rec.tx_hash.clone(),
                reason: rec.reason,
                provider_country_id: rec.provider_country_id,
                total_fee_v2: rec.total_fee_v2,
            })
            .collect())
    }

    pub async fn get_sessions_by_provider(
        &mut self,
        provider_addr: String,
    ) -> anyhow::Result<Vec<StorageSession>> {
        let records = sqlx::query!(
            r#"
                SELECT * 
                FROM sessions 
                WHERE provider_addr = $1 AND status <> $2
                ORDER BY handshake_at DESC
                LIMIT 20
            "#,
            provider_addr,
            SessionStatus::Active as i32
        )
        .fetch_all(self.storage.conn())
        .await?;

        Ok(records
            .into_iter()
            .map(|rec| StorageSession {
                session_hash: rec.session_hash.clone(),
                client_identifier: rec.client_identifier.clone(),
                provider_addr: rec.provider_addr.clone(),
                client_addr: rec.client_addr.clone(),
                rate_per_kb_v2: rec.rate_per_kb_v2,
                rate_per_kb: rec.rate_per_kb,
                handshake_at: rec.handshake_at,
                end_at: rec.end_at,
                duration: rec.duration,
                bandwidth_usage: rec.bandwidth_usage,
                duration_fee: rec.duration_fee,
                bandwidth_fee: rec.bandwidth_fee,
                total_fee: rec.total_fee,
                status: rec.status,
                tx_hash: rec.tx_hash.clone(),
                reason: rec.reason,
                provider_country_id: rec.provider_country_id,
                total_fee_v2: rec.total_fee_v2,
            })
            .collect())
    }
}
