use anyhow::Error;

use crate::StorageProcessor;

#[derive(Debug)]
pub struct SessionInternalTxsDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl SessionInternalTxsDal<'_, '_> {
    pub async fn create_session_internal_txs(
        &mut self,
        session_hash: String,
        tx_hash: String,
    ) -> anyhow::Result<()> {
        match sqlx::query!(
            r#"
                INSERT INTO session_internal_txs (session_hash, tx_hash) VALUES($1, $2);
            "#,
            session_hash,
            tx_hash,
        )
        .execute(self.storage.conn())
        .await
        {
            Ok(_) => Ok(()),
            Err(e) => {
                return Err(Error::msg(format!("failed to create session_internal_txs, err={}", e)));
            }
        }
    }
}
