use anyhow::{Context, Error};

use crate::{
    model::{storage_tier::StorageUserTier, storage_tier_point::StorageTierPoint},
    StorageProcessor,
};
use sqlx::types::chrono::Utc;

const MAX_I64_VALUE: i64 = 9223372036854775807;

#[derive(Debug)]
pub struct TierDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl TierDal<'_, '_> {
    pub async fn create_tier(&mut self, user_addr: String) -> anyhow::Result<()> {
        let mut transaction = self
            .storage
            .start_transaction()
            .await
            .context("start_transaction()")?;

        match sqlx::query!(
            r#"
                INSERT INTO user_tiers (user_addr)
                VALUES ($1)
                ON CONFLICT (user_addr) DO NOTHING;
            "#,
            user_addr,
        )
        .execute(transaction.conn())
        .await
        {
            Ok(rs) => match rs.rows_affected() {
                1 => {
                    transaction.commit().await.context("commit()")?;
                    return Ok(());
                }
                _ => {
                    transaction.commit().await.context("rollback()")?;
                    return Err(Error::msg(format!("failed to create tier")));
                }
            },
            Err(e) => {
                transaction.commit().await.context("rollback()")?;
                return Err(Error::msg(format!(
                    "failed to execute insert user tier query, err = {}",
                    e
                )));
            }
        }
    }

    pub async fn get_user_tier_by(&mut self, user_addr: String) -> anyhow::Result<StorageUserTier> {
        match sqlx::query!(
            r#"
                SELECT *
                FROM user_tiers
                WHERE user_addr = $1
            "#,
            user_addr,
        )
        .fetch_one(self.storage.conn())
        .await
        {
            Ok(rs) => {
                return Ok(StorageUserTier {
                    user_addr: rs.user_addr,
                    points: rs.points,
                })
            }
            Err(e) => {
                return Err(Error::msg(format!(
                    "failed to execute select user tier by address, err = {}",
                    e
                )));
            }
        }
    }

    pub async fn get_user_tier_points(
        &mut self,
        user_addr: String,
    ) -> anyhow::Result<Vec<StorageTierPoint>> {
        let rec = sqlx::query!(
            r#"
                SELECT *
                FROM user_tier_points
                WHERE user_addr = $1
                ORDER BY created_at DESC
                LIMIT 20;
            "#,
            user_addr,
        )
        .fetch_all(self.storage.conn())
        .await?;

        Ok(rec
            .iter()
            .map(|tp| StorageTierPoint {
                user_addr: tp.user_addr.clone(),
                points: tp.points,
                created_at: tp.created_at,
            })
            .collect())
    }

    pub async fn add_user_tier_points(
        &mut self,
        user_addr: String,
        points: i64,
    ) -> anyhow::Result<()> {
        let mut transaction = self
            .storage
            .start_transaction()
            .await
            .context("start_transaction()")?;

        sqlx::query!(
            r#"
                INSERT INTO user_tier_points (user_addr, points, created_at)
                VALUES ($1, $2, $3);
            "#,
            user_addr,
            points,
            Utc::now().timestamp(),
        )
        .execute(transaction.conn())
        .await?;

        sqlx::query!(
            r#"
                UPDATE user_tiers 
                SET points = 
                    CASE 
                        WHEN points + $1 > $2 THEN $2 
                        ELSE points + $1 
                    END
                WHERE user_addr = $3;
            "#,
            points,
            MAX_I64_VALUE,
            user_addr,
        )
        .execute(transaction.conn())
        .await?;

        transaction.commit().await.context("commit()")?;
        return Ok(());
    }
}
