use dpn_core::types::tx::{Tx, TxStatus, TxType};

use crate::{model::storage_tx::StorageTx, StorageProcessor};

#[derive(Debug)]
pub struct TxDal<'a, 'c> {
    pub storage: &'a mut StorageProcessor<'c>,
}

impl TxDal<'_, '_> {
    pub async fn insert_tx(&mut self, tx: Tx) -> anyhow::Result<()> {
        let _tx: StorageTx = tx.into();

        sqlx::query!(
            r#"
                INSERT INTO transactions (
                    tx_hash,
                    from_addr,
                    to_addr,
                    amount,
                    tx_type,
                    tx_status,
                    chain_tx_hash,
                    created_at
                ) VALUES (
                    $1,
                    $2,
                    $3,
                    $4,
                    $5,
                    $6,
                    $7,
                    $8
                )
                ON CONFLICT (tx_hash) DO NOTHING;
            "#,
            _tx.tx_hash,
            _tx.from_addr,
            _tx.to_addr,
            _tx.amount,
            _tx.tx_type,
            _tx.tx_status,
            _tx.chain_tx_hash,
            _tx.created_at,
        )
        .execute(self.storage.conn())
        .await?;

        Ok(())
    }

    /// Update a tx.
    pub async fn update_tx(
        &mut self,
        tx_hash: String,
        chain_tx_hash: Option<String>,
        amount: i64,
        tx_status: i32,
    ) -> anyhow::Result<StorageTx> {
        let rec = sqlx::query!(
            r#"
                UPDATE transactions 
                SET 
                    chain_tx_hash = $2, 
                    amount = $3, 
                    tx_status = $4
                WHERE tx_hash = $1
                RETURNING *
            "#,
            tx_hash,
            chain_tx_hash,
            amount,
            tx_status as i32,
        )
        .fetch_one(self.storage.conn())
        .await?;

        Ok(StorageTx {
            tx_hash: rec.tx_hash,
            from_addr: rec.from_addr,
            to_addr: rec.to_addr,
            amount: rec.amount,
            tx_type: rec.tx_type,
            tx_status: rec.tx_status,
            chain_tx_hash: rec.chain_tx_hash,
            created_at: rec.created_at,
        })
    }

    pub async fn get_next_pending_tx(&mut self) -> anyhow::Result<Option<StorageTx>> {
        let rec = sqlx::query!(
            r#"
                SELECT *
                FROM transactions 
                WHERE tx_status = $1
                ORDER BY created_at ASC
                LIMIT 1
            "#,
            TxStatus::Pending as i32,
        )
        .fetch_optional(self.storage.conn())
        .await?;

        Ok(rec.map(|rec| StorageTx {
            tx_hash: rec.tx_hash,
            from_addr: rec.from_addr,
            to_addr: rec.to_addr,
            amount: rec.amount,
            tx_type: rec.tx_type,
            tx_status: rec.tx_status,
            chain_tx_hash: rec.chain_tx_hash,
            created_at: rec.created_at,
        }))
    }

    pub async fn get_withdrawal_txs_of(
        &mut self,
        user_addr: String,
    ) -> anyhow::Result<Vec<StorageTx>> {
        let rec = sqlx::query!(
            r#"
                SELECT * 
                FROM transactions
                WHERE from_addr = $1 AND tx_type = $2
                ORDER BY created_at DESC LIMIT 20
            "#,
            user_addr,
            TxType::Withdrawal as i32,
        )
        .fetch_all(self.storage.conn())
        .await?;

        Ok(rec
            .iter()
            .map(|tx| StorageTx {
                tx_hash: tx.tx_hash.clone(),
                from_addr: tx.from_addr.clone(),
                to_addr: tx.to_addr.clone(),
                amount: tx.amount,
                tx_type: tx.tx_type,
                tx_status: tx.tx_status,
                chain_tx_hash: tx.chain_tx_hash.clone(),
                created_at: tx.created_at,
            })
            .collect())
    }

    pub async fn get_total_pending_withdrawal_txs_of(
        &mut self,
        user_addr: String,
    ) -> anyhow::Result<Option<i64>> {
        let rec = sqlx::query!(
            r#"
                SELECT COUNT(*) as total_pending_withdrawals
                FROM transactions
                WHERE from_addr = $1 AND tx_type = $2 AND tx_status = $3
            "#,
            user_addr,
            TxType::Withdrawal as i32,
            TxStatus::Pending as i32,
        )
        .fetch_one(self.storage.conn())
        .await?;

        Ok(rec.total_pending_withdrawals)
    }
}
