use crate::{model::storage_user_online_session::StorageUserOnlineSession, StorageProcessor};
use anyhow::anyhow;

#[derive(Debug)]
pub struct UserOnlineSessionDal<'a, 'c> {
    pub(crate) storage: &'a mut StorageProcessor<'c>,
}

impl UserOnlineSessionDal<'_, '_> {
    pub async fn insert_session(
        &mut self,
        session: StorageUserOnlineSession,
    ) -> anyhow::Result<()> {
        match sqlx::query!(
            r#"
                INSERT INTO user_online_sessions (
                    user_addr, earned_lp, start_time, end_time, created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6)
            "#,
            session.user_addr,
            session.earned_lp,
            session.start_time,
            session.end_time,
            session.created_at,
            session.updated_at
        )
        .execute(self.storage.conn())
        .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow!(
                "Failed to execute insert_session query, error: {}",
                e
            )),
        }
    }

    pub async fn get_sessions_by_user(
        &mut self,
        user_addr: String,
    ) -> anyhow::Result<Vec<StorageUserOnlineSession>> {
        match sqlx::query!(
            r#"
                SELECT user_addr, earned_lp, start_time, end_time, created_at, updated_at
                FROM user_online_sessions
                WHERE user_addr = $1
                ORDER BY start_time DESC
            "#,
            user_addr
        )
        .fetch_all(self.storage.conn())
        .await
        {
            Ok(records) => Ok(records
                .iter()
                .map(|rec| StorageUserOnlineSession {
                    user_addr: rec.user_addr.clone(),
                    earned_lp: rec.earned_lp,
                    start_time: rec.start_time,
                    end_time: rec.end_time,
                    created_at: rec.created_at,
                    updated_at: rec.updated_at,
                })
                .collect()),
            Err(e) => Err(anyhow!(
                "Failed to execute get_sessions_by_user query, error: {}",
                e
            )),
        }
    }

    pub async fn get_session_by_user_online(
        &mut self,
        user_addr: String,
        start_time: i64,
    ) -> anyhow::Result<StorageUserOnlineSession> {
        match sqlx::query!(
            r#"
            SELECT user_addr, earned_lp, start_time, end_time, created_at, updated_at
            FROM user_online_sessions
            WHERE user_addr = $1 AND start_time = $2
            "#,
            user_addr,
            start_time
        )
        .fetch_one(self.storage.conn())
        .await
        {
            Ok(rec) => Ok(StorageUserOnlineSession {
                user_addr: rec.user_addr,
                earned_lp: rec.earned_lp,
                start_time: rec.start_time,
                end_time: rec.end_time,
                created_at: rec.created_at,
                updated_at: rec.updated_at,
            }),
            Err(e) => {
                Err(anyhow!(
                    "online session not found: {}",
                    e
                ))
            }
        }
    }
    

    pub async fn update_end_time(
        &mut self,
        user_addr: String,
        start_time: i64,
        end_time: i64,
        earned_lp: f64
    ) -> anyhow::Result<()> {
        match sqlx::query!(
            r#"
                UPDATE user_online_sessions
                SET end_time = $1, updated_at = $2, earned_lp = $3
                WHERE user_addr = $4 AND start_time = $5
            "#,
            end_time,
            sqlx::types::chrono::Utc::now().timestamp(),  
            earned_lp,
            user_addr,
            start_time
        )
        .execute(self.storage.conn())
        .await
        {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow!(
                "Failed to update end_time, error: {}",
                e
            )),
        }
    }

}
