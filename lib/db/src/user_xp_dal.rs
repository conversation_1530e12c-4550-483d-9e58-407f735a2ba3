use sqlx::types::chrono::Utc;
use crate::StorageProcessor;

#[derive(Debug)]
pub struct UserXpDal<'a, 'c> {
    pub storage: &'a mut StorageProcessor<'c>,
}

impl UserXpDal<'_, '_> {
    pub async fn insert(
        &mut self,
        user_addr: String,
        minutes_uptime: f64,
    ) -> anyhow::Result<()> {
        sqlx::query!(
            r#"
                INSERT INTO user_xps (
                    user_addr,
                    minutes_uptime,
                    created_at,
                    updated_at
                )
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (user_addr) DO NOTHING;
            "#,
            user_addr,
            minutes_uptime,
            Utc::now().timestamp(),
            Utc::now().timestamp(),
        )
        .execute(self.storage.conn())
        .await?;

        Ok(())
    }

    pub async fn update_uptime(
        &mut self,
        user_addr: String,
        minutes_uptime: f64,
    ) -> anyhow::Result<()> {
        sqlx::query!(
            r#"
                UPDATE user_xps
                SET minutes_uptime = minutes_uptime + $1
                WHERE user_addr = $2;
            "#,
            minutes_uptime,
            user_addr,
        )
        .execute(self.storage.conn())
        .await?;

        Ok(())
    }

    pub async fn get_uptime(
        &mut self,
        user_addr: String,
    ) -> anyhow::Result<f64> {
        let result = sqlx::query!(
            r#"
                SELECT minutes_uptime
                FROM user_xps
                WHERE user_addr = $1;
            "#,
            user_addr,
        )
        .fetch_one(self.storage.conn())
        .await?;

        Ok(result.minutes_uptime)
    }
}
