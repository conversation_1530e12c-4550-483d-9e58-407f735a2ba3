[package]
name = "dpn_redis_2"
version = "0.0.1"
edition = "2021"
license = "Apache-2.0"
description = "Subnet DPN redis 2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]

web3 = { version = "0.19.0", default-features = false }
serde = { version = "1.0.152", features = ["derive"] }
serde_json = "1.0.91"
num-derive = "0.4.1"
num-traits = "0.2.17"
utoipa = { version = "4.1.0", default-features = false }
sha3 = "0.9"
hex = "0.4"
num = { version = "0.3.1", features = ["serde"] }
ethers = "2.0.10"
"prost" = "0.11.0"
"prost-types" = "0.11.1"
chrono = "0.4.31"
anyhow = "1.0.75"
redis = { version = "0.28.2", features = ["tls-rustls", "tokio-rustls-comp"] }
async-trait = "0.1.73"
log = "0.4.20"
mockall = { version = "0.11.2", features = ["nightly"] }
redis-async = { version = "0.17.1", features = ["with-rustls"] }
url = "2.5.0"
tokio = { version = "1.37.0", features = ["time"] }
actix-web = "4.3.1"
reqwest = { version = "0.11.18", features = ["json", "native-tls-crate"] }
utoipa-swagger-ui = { version = "5.0.0", features = ["actix-web"] }
maxminddb = "0.24.0"
# r2d2 = "0.8.10"
# r2d2_redis = { version = "0.14.0" }
urlencoding = "2.1.2"
bb8-redis = { version = "0.20.0" }
rustls = "0.23.23"
tokio-rustls = "0.25.0"  # Add this dependency