use anyhow::{anyhow, Error, Result};
// use r2d2_redis::redis::Commands;
use redis::{Commands as _, Connection, RedisResult};
use redis_async::client::{ConnectionBuilder, PubsubConnection};
use serde::de::DeserializeOwned;
use serde::Serialize;
use std::{collections::HashMap, fmt::Debug, sync::Arc};
use url::Url;
// use r2d2_redis::RedisConnectionManager;
// use r2d2::Pool;
use redis::ConnectionInfo;
use bb8_redis::{
    bb8,
    redis::{cmd, AsyncCommands},
    RedisConnectionManager,
};
use bb8::Pool;

struct RedisUri {
    is_tls: bool,
    password: Option<String>,
    host: String,
    port: u16,
}

#[derive(Debug)]
pub struct RedisService {
    client: redis::Client,
    pubsub_con: PubsubConnection,
    pool: Pool<RedisConnectionManager>,
}

impl RedisService {
    pub async fn new(redis_uri: &str) -> Result<Self, Error> {        
        let manager = RedisConnectionManager::new(redis_uri)
            .map_err(|e| anyhow!("Failed to create Redis manager: {}", e))?;

        let pool = bb8::Pool::builder().max_size(100).build(manager).await?;
        
        let conn_builder = Self::get_redis_conn_builder_from_uri(redis_uri)?;
        let pubsub_con = conn_builder
            .pubsub_connect()
            .await
            .map_err(|e| anyhow!("create pub sub connection failed err={}", e))?;
        

        Ok(Self { 
            pool,
            client: redis::Client::open(redis_uri)?,
            pubsub_con,
        })
    }

    fn parse_redis_uri(redis_uri: &str) -> Result<RedisUri> {
        let parsed_url = Url::parse(redis_uri)?;

        let is_tls = match parsed_url.scheme() {
            "redis" => false,
            "rediss" => true,
            unknown => {
                return Err(anyhow::anyhow!(
                    "invalid scheme, must be 'redis' or 'rediss' unknown {}",
                    unknown
                ))
            }
        };
        let password = parsed_url.password().map(|p| p.to_string());

        let host = match parsed_url.host_str() {
            Some(host) => host.to_string(),
            None => return Err(anyhow::anyhow!("parse host failed")),
        };

        let port = match parsed_url.port() {
            Some(port) => port,
            None => return Err(anyhow::anyhow!("parse port failed")),
        };

        Ok(RedisUri {
            is_tls,
            password,
            host,
            port,
        })
    }

    pub fn get_redis_conn_builder_from_uri(redis_uri: &str) -> Result<ConnectionBuilder> {
        let redis_info =
            Self::parse_redis_uri(redis_uri).map_err(|e| anyhow!("parse failed err={}", e))?;

        let mut connection_builder: ConnectionBuilder =
            ConnectionBuilder::new(redis_info.host, redis_info.port)
                .map_err(|e| anyhow!("connection build create failed err={}", e))?;

        if redis_info.is_tls {
            connection_builder.tls();
        }

        if let Some(redis_password) = redis_info.password {
            connection_builder.password(redis_password);
        }

        Ok(connection_builder)
    }
    pub fn get_conn_info(self: Arc<Self>) -> ConnectionInfo {
        self.client.get_connection_info().clone()
    }
    pub fn get_pubsub_conn(self: Arc<Self>) -> PubsubConnection {
        self.pubsub_con.clone()
    }

    pub async fn hset<T>(self: Arc<Self>, key: String, field: String, obj: T) -> Result<(), Error>
    where
        T: Serialize,
    {
        let mut conn = self.pool.get().await
            .map_err(|e| anyhow!("Failed to get Redis connection from pool: {}", e))?;

        let value = serde_json::to_string(&obj)
            .map_err(|e| anyhow!("Failed to serialize object: {}", e))?;

        (&mut *conn).hset(&key, &field, value)
            .await
            .map_err(|e| anyhow!("Redis HSET failed: key={}, field={}, err={}", key, field, e))?;

        Ok(())
    }

    pub async fn hget<T>(self: Arc<Self>, key: String, field: String) -> Result<T, Error>
    where
        T: Clone + DeserializeOwned,
    {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        let obj_str: String = conn
            .hget(key.clone(), field.clone())
            .map_err(|e| anyhow!("redis cannot get key={}:{} err={}", key, field, e))?;
        let t = serde_json::from_str::<T>(&obj_str)
            .map_err(|e| anyhow!("redis failed to decode err={}", e))?;
        Ok(t)
    }


    pub fn hgetall<T>(self: Arc<Self>, key: String) -> Result<Vec<(String, T)>, Error>
    where
        T: Clone + DeserializeOwned,
    {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        let result: HashMap<String, String> = conn
            .hgetall(key.clone())
            .map_err(|e| anyhow!("redis cannot get key={} err={}", key, e))?;
        let mut rs: Vec<(String, T)> = vec![];
        for (key, obj_str) in result.iter() {
            let proxy_acc = serde_json::from_str::<T>(&obj_str)
                .map_err(|e| anyhow!("redis failed to decode err={}", e))?;
            rs.push((key.clone(), proxy_acc.clone()));
        }
        Ok(rs)
    }

    pub fn hdel(self: Arc<Self>, key: String, field: String) -> Result<(), Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        conn.hdel(key.clone(), field.clone())
            .map_err(|e| anyhow!("redis cannot hdel key={} field={} err={}", key, field, e))?;
        Ok(())
    }

    pub async fn exists(self: Arc<Self>, key: String) -> Result<bool, Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        Ok(conn.exists(key.clone())?)
    }

    pub async fn expire(self: Arc<Self>, key: String, seconds: u32) -> Result<(), Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        Ok(conn.expire(key.clone(), seconds as i64)?)
    }

    pub async fn keys(self: Arc<Self>, pattern: String) -> Result<Vec<String>, Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        
        let keys: Vec<String> = conn
            .keys(pattern.clone())
            .map_err(|e| anyhow!("redis failed to get keys with pattern={} err={}", pattern, e))?;
        
        Ok(keys)
    }
 
    pub async fn scan(
        self: Arc<Self>, 
        pattern: String, 
        cursor: u64, 
        count: u64
    ) -> Result<(u64, Vec<String>), Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        
        // Create a scan operation with the pattern
        let scan = redis::cmd("SCAN")
            .arg(cursor)
            .arg("MATCH")
            .arg(&pattern)
            .arg("COUNT")
            .arg(count)
            .query::<(u64, Vec<String>)>(&mut conn)
            .map_err(|e| anyhow!("redis scan failed with pattern={} err={}", pattern, e))?;
        
        Ok(scan)
    }

    pub async fn hscan(
        self: Arc<Self>, 
        key: String, 
        pattern: String, 
        cursor: u64, 
        count: u64
    ) -> Result<(u64, HashMap<String, String>), Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        
        let (next_cursor, fields): (u64, HashMap<String, String>) = redis::cmd("HSCAN")
            .arg(&key)
            .arg(cursor)
            .arg("MATCH")
            .arg(&pattern)
            .arg("COUNT")
            .arg(count)
            .query(&mut conn)
            .map_err(|e| anyhow!("redis hscan failed for key={} pattern={} err={}", key, pattern, e))?;
        
        Ok((next_cursor, fields))
    }

    pub fn pipeline(self: Arc<Self>) -> RedisPipeline {
        RedisPipeline {
            redis_svc: self,
            commands: Vec::new(),
        }
    }

    pub fn zadd(self: Arc<Self>, key: String, score: u32, value: u32) -> Result<(), Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        match conn.zadd::<String, u32, u32, ()>(key, value, score) {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow!(
                "redis failed to insert peer into peer queue err={}",
                e
            )),
        }
    }

    pub fn zrem(self: Arc<Self>, key: String, value: u32) -> Result<(), anyhow::Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;

        match conn.zrem::<String, u32, usize>(key, value) {
            Ok(_) => Ok(()),
            Err(e) => Err(anyhow!(
                "redis failed to remove peer in peer queue err={}",
                e
            )),
        }
    }

    pub fn zsetall(self: Arc<Self>, key: String, score: u32) -> Result<(), anyhow::Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;

        let elements: Vec<(u32, u32)> = conn
            .zrange_withscores(key.clone(), 0, -1)
            .map_err(|e| anyhow!("redis failed to get sorted set err={}", e))?;

        for (value, _) in elements {
            conn.zadd::<String, u32, u32, ()>(key.clone(), value, score)
                .map_err(|e| anyhow!("redis failed to set scores err={}", e))?;
        }

        Ok(())
    }

    pub fn zgetall(self: Arc<Self>, key: String) -> Result<Vec<(u32, u32)>, Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;

        let elements: Vec<(u32, u32)> = conn
            .zrange_withscores(key.clone(), 0, -1)
            .map_err(|e| anyhow!("redis failed to get peer queue err={}", e))?;

        let mut result: Vec<(u32, u32)> = elements
            .into_iter()
            .map(|(value, score)| (value, score))
            .collect();

        result.sort_by_key(|(_value, score)| *score);

        Ok(result)
    }

    /// this function is used to delete data of given key
    pub fn del(self: Arc<Self>, key: String) -> Result<(), Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;

        conn.del(key.clone())
            .map_err(|e| anyhow!("redis failed to delete key={} err={}", key, e))
    }

    pub async fn publish(self: Arc<Self>, chan_name: String, obj_str: String) -> Result<(), Error> {
        let mut conn = self
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        conn.publish::<_, _, ()>(&chan_name, &obj_str)?;
        Ok(())
    }

    pub async fn get_conn(self: Arc<Self>) -> RedisResult<Connection> {
        self.client.get_connection()
    }
}

pub struct RedisPipeline {
    redis_svc: Arc<RedisService>,
    commands: Vec<redis::Pipeline>,
}

impl std::fmt::Debug for RedisPipeline {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("RedisPipeline")
            .field("redis_svc", &self.redis_svc)
            .field("commands_count", &self.commands.len())
            .finish()
    }
}

impl RedisPipeline {
    pub fn hdel(&mut self, key: &str, field: &str) -> &mut Self {
        let mut pipe = redis::pipe();
        pipe.hdel(key, field);
        self.commands.push(pipe);
        self
    }
    
    pub fn hset<T>(&mut self, key: &str, field: &str, value: T) -> &mut Self 
    where 
        T: Serialize,
    {
        let mut pipe = redis::pipe();
        let value_str = serde_json::to_string(&value).unwrap_or_default();
        pipe.hset(key, field, value_str);
        self.commands.push(pipe);
        self
    }
    
    pub fn del(&mut self, key: &str) -> &mut Self {
        let mut pipe = redis::pipe();
        pipe.del(key);
        self.commands.push(pipe);
        self
    }
    
    pub async fn execute(&self) -> Result<(), Error> {
        let mut conn = self.redis_svc
            .client
            .get_connection()
            .map_err(|e| anyhow!("cannot get connection err={}", e))?;
        
        for pipe in &self.commands {
            pipe.query::<()>(&mut conn)
                .map_err(|e| anyhow!("pipeline execution failed err={}", e))?;
        }
        
        Ok(())
    }
}
