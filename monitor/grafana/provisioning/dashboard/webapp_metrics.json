{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"collapsed": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 14, "panels": [], "title": "Web application metrics", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 1}, "id": 1, "interval": "5s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "rate(http_requests_total[5m])", "interval": "", "legendFormat": "{{method}} {{path}}", "refId": "A"}], "title": "HTTP requests per second", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 1}, "id": 3, "interval": "5s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.1.4", "targets": [{"exemplar": true, "expr": "rate(http_response_time_seconds_sum[5m]) / rate(http_response_time_seconds_count[5m])", "interval": "", "legendFormat": "{{method}} {{path}}", "refId": "A"}], "title": "Mean response time", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 0, "y": 12}, "id": 18, "options": {"reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.1.4", "targets": [{"exemplar": true, "expr": "increase(http_requests_total{method=\"GET\",path=\"/planets\"}[1m])", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Requests number for the last minute", "type": "gauge"}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateBlues", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": null, "gridPos": {"h": 12, "w": 21, "x": 3, "y": 12}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 16, "interval": null, "legend": {"show": false}, "maxDataPoints": null, "pluginVersion": "8.1.4", "reverseYBuckets": false, "targets": [{"exemplar": true, "expr": "sum(increase(http_response_time_seconds_bucket{path=\"/planets\"}[30s])) by (le)", "format": "heatmap", "interval": "", "legendFormat": "{{le}}", "refId": "A"}], "timeFrom": null, "title": "Response time distribution over time (`GET /planets`)", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": 10, "xBucketSize": null, "yAxis": {"decimals": 2, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": 10, "yBucketSize": null}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 0, "y": 18}, "id": 2, "interval": "5s", "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.1.4", "targets": [{"exemplar": true, "expr": "http_connected_sse_clients", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Connected SSE clients", "type": "gauge"}, {"collapsed": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 12, "panels": [], "title": "System metrics", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 25}, "id": 4, "interval": "5s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "rate(process_cpu_seconds_total{job=\"admin\"}[1m])", "interval": "", "legendFormat": "process", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(container_cpu_usage_seconds_total{name='admin'}[1m])) by (name)", "interval": "", "legendFormat": "container", "refId": "B"}], "title": "CPU usage", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisLabel": "MiB", "axisPlacement": "left", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 25}, "id": 5, "interval": "5s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "process_resident_memory_bytes{job=\"admin\"} / 1024 / 1024", "interval": "", "legendFormat": "process", "refId": "A"}, {"exemplar": true, "expr": "container_memory_usage_bytes{name=\"admin\"} / 1024 / 1024", "interval": "", "legendFormat": "container", "refId": "B"}], "title": "Memory usage", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 1, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m"]}, "timezone": "", "title": "webapp_metrics", "uid": "7wcZ94dnz", "version": 1}