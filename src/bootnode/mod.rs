pub mod utils;

use self::utils::get_geo_from_ip_address;
use crate::connection::ConnectionService;
use crate::connection::RedisAlgoProxyInfo;
use crate::webapi::metrics::MasternodeMetric;
use crate::APP_CONFIG;
use anyhow::{anyhow, Error, Result};
use async_trait::async_trait;
use dpn_core::services::redis::DPNRedisKey;
use dpn_core::services::redis::RedisService;
use dpn_redis_2::RedisService as RedisService2;
use dpn_core::services::types::PeerChangedInfo;
use dpn_core::types::connection::PeernodeInfo;
use dpn_core::types::geo::DEFAULT_CONTINENTAL_CODE;
use dpn_core::types::masternode::ActivePeersClients;
use dpn_core::types::masternode::MasternodeInfo;
use log::{debug, error, info};
use mockall::automock;
use reqwest::StatusCode;
use static_init::dynamic;
use utils::get_ip_bits_from_ip_address;
use std::collections::HashMap;
use std::fs::File;
use std::io::BufReader;
use std::{fmt::Debug, sync::Arc};
use tokio::sync::RwLock;
use serde_json::json;
use dashmap::DashMap;
use futures::future;

#[dynamic]
pub static HTTP_CLIENT: reqwest::Client = {
    let mut headers = reqwest::header::HeaderMap::new();
    headers.insert(
        "Content-Type",
        reqwest::header::HeaderValue::from_static("application/json"),
    );

    headers.insert(
        "x-api-key",
        reqwest::header::HeaderValue::from_static(&APP_CONFIG.x_api_key),
    );

    reqwest::Client::builder()
        .default_headers(headers)
        .build()
        .unwrap()
};

#[automock]
#[async_trait]
pub trait BootnodeService: Debug + Send + Sync + 'static {
    async fn register_masternode(
        self: Arc<Self>,
        masternode_ip_addr: String,
        masternode_info: MasternodeInfo,
    ) -> Result<(), Error>;
    async fn deregister_masternode(
        self: Arc<Self>,
        masternode_ip_addr: String,
    ) -> Result<(), Error>;
    async fn assign_masternode(self: Arc<Self>, geoname_id: u32) -> Option<MasternodeInfo>;
    async fn online_masternodes(self: Arc<Self>) -> Vec<(String, MasternodeInfo)>;
    async fn online_peers(self: Arc<Self>) -> Result<Vec<PeernodeInfo>>;
    async fn masternode_metrics(self: Arc<Self>) -> Result<Vec<MasternodeMetric>>;
    async fn get_masternode_map(self: Arc<Self>) -> DashMap<i64, String>;
    async fn force_stop_peer(self: Arc<Self>, peer_ip: String) -> Result<()>;
    async fn check_peer(self: Arc<Self>, id_session: String) -> Option<(String,String)>;
}

#[derive(Debug)]
pub struct BootnodeServiceImpl {
    pub registered_masternodes: Arc<RwLock<HashMap<String, (String, MasternodeInfo)>>>,
    pub registered_masternodes_clone: DashMap<String, (String, MasternodeInfo)>,
    pub conn_svc: Arc<dyn ConnectionService>,

    // paths
    pub online_peers: String,
    pub masternode_metrics: String,
    pub force_stop_peer: String,
    // map masternode to country code
    pub masternode_country_code_map: DashMap<i64, String>,
    redis_svc: Arc<RedisService>,
    redis_svc_2: Arc<RedisService2>,
    
}

#[async_trait]
impl BootnodeService for BootnodeServiceImpl {
    async fn register_masternode(
        self: Arc<Self>,
        masternode_ip_addr: String,
        masternode_info: MasternodeInfo,
    ) -> Result<(), Error> {
        let continent_code: String = match get_geo_from_ip_address(masternode_ip_addr.clone()) {
            Ok(geo) => geo
                .continent
                .and_then(|c| c.code)
                .unwrap_or(DEFAULT_CONTINENTAL_CODE.to_string()),
            Err(_) => DEFAULT_CONTINENTAL_CODE.to_string(),
        };
        println!("continent_code={:?}", continent_code);
        let mut masternodes_wlock = self.registered_masternodes.write().await;
        masternodes_wlock.insert(
            masternode_ip_addr.to_string(),
            (continent_code.clone(), masternode_info.clone()),
        );
        self.registered_masternodes_clone.insert(
            masternode_ip_addr.to_string(),
            (continent_code.clone(), masternode_info.clone()),
        );
        Ok(())
    }

    async fn deregister_masternode(
        self: Arc<Self>,
        masternode_ip_addr: String,
    ) -> Result<(), Error> {
        let _self = self.clone();
        let mut masternodes_wlock = _self.registered_masternodes.write().await;
        masternodes_wlock.remove(&masternode_ip_addr.clone());
        _self.registered_masternodes_clone.remove(&masternode_ip_addr.clone());

        let conn_svc = self.clone().conn_svc.clone();
        if let Err(e) = conn_svc.on_masternode_deregistered().await {
            error!("handle masternode deregister failed err={}", e);
        }

        info!("deregistered masternode {}", masternode_ip_addr);
        Ok(())
    }

    async fn assign_masternode(self: Arc<Self>, geoname_id: u32) -> Option<MasternodeInfo> {
        let geoname_id_i64 = geoname_id as i64;
        
        // First try to get from country code map
        if let Some(masternode_id) = self.masternode_country_code_map.get(&geoname_id_i64) {
            return self.registered_masternodes_clone
                .get(masternode_id.value())
                .map(|entry| entry.value().1.clone());
        }
        
        info!("no masternode found in country code map, fallback to AS continent");
        // Fallback to AS continent
        self.registered_masternodes_clone
            .iter()
            .find(|entry| entry.value().0 == "AS")
            .map(|entry| entry.value().1.clone())
    }

    async fn online_masternodes(self: Arc<Self>) -> Vec<(String, MasternodeInfo)> {
        let masternodes_rlock = self.registered_masternodes.read().await;
        masternodes_rlock
            .iter()
            .map(|(_, (continent_code, info))| (continent_code.to_string(), info.clone()))
            .collect()
    }

    async fn online_peers(self: Arc<Self>) -> Result<Vec<PeernodeInfo>> {
        let masternodes_rlock = self.registered_masternodes.read().await;
        let masternode_urls = masternodes_rlock
            .values()
            .map(|(_, ms)| ms.web_bind.clone())
            .collect::<Vec<String>>();
        drop(masternodes_rlock);

        let mut online_peers: Vec<PeernodeInfo> = vec![];

        for masternode_url in masternode_urls {
            let path = format!("http://{}/{}", masternode_url, self.online_peers.clone());

            match HTTP_CLIENT.get(path.clone()).send().await {
                Ok(response) => {
                    if response.status() != StatusCode::OK {
                        return Err(Error::msg(format!(
                            "failed to get online peers: unknown status code={}",
                            response.status()
                        )));
                    } else {
                        match response.json::<Vec<PeernodeInfo>>().await {
                            Ok(mut peers) => {
                                online_peers.append(&mut peers);
                            }
                            _ => {}
                        }
                    }
                }
                Err(e) => {
                    return Err(Error::msg(format!(
                        "failed to get online peers: request failed err={}",
                        e
                    )));
                }
            };
        }

        Ok(online_peers)
    }

    async fn masternode_metrics(self: Arc<Self>) -> Result<Vec<MasternodeMetric>> {
        let masternodes_rlock = self.registered_masternodes.read().await;
        let masternode_urls = masternodes_rlock
            .values()
            .map(|(_, ms)| ms.web_bind.clone())
            .collect::<Vec<String>>();
        let masternode_ids = masternodes_rlock.keys().collect::<Vec<&String>>();

        let mut masternode_metrics: Vec<MasternodeMetric> = vec![];

        for (index, masternode_url) in masternode_urls.iter().enumerate() {
            let path = format!(
                "http://{}/{}",
                masternode_url,
                self.masternode_metrics.clone()
            );

            match HTTP_CLIENT.get(path.clone()).send().await {
                Ok(response) => {
                    if response.status() != StatusCode::OK {
                        return Err(Error::msg(format!(
                            "failed to get masternode metrics: unknown status code={}",
                            response.status()
                        )));
                    } else {
                        match response.json::<ActivePeersClients>().await {
                            Ok(active_peers_clients) => {
                                let masternode_metric = MasternodeMetric {
                                    masternode_ip_addr: masternode_ids
                                        .get(index)
                                        .unwrap()
                                        .to_string(),
                                    active_peers: active_peers_clients.active_peers,
                                    active_clients: active_peers_clients.active_clients,
                                };
                                masternode_metrics.push(masternode_metric);
                            }
                            _ => {}
                        }
                    }
                }
                Err(e) => {
                    return Err(Error::msg(format!(
                        "failed to get masternode metrics: request failed err={}",
                        e
                    )));
                }
            };
        }

        Ok(masternode_metrics)
    }

    async fn get_masternode_map(self: Arc<Self>) -> DashMap<i64, String> {
        self.masternode_country_code_map.clone()
    }

    async fn force_stop_peer(self: Arc<Self>, peer_ip: String) -> Result<()> {
        let masternodes_rlock = self.registered_masternodes.read().await;
        let masternode_urls = masternodes_rlock
            .values()
            .map(|(_, ms)| ms.client_bind.clone())
            .collect::<Vec<String>>();
        drop(masternodes_rlock); // Release the lock before spawning tasks

        let body = json!({
            "peer_ip": peer_ip.clone()
        });

        let mut handles = Vec::new();
        
        // Create a task for each masternode request
        for masternode_url in masternode_urls {
            // url  "************:x",
            //   "client_bind": "************:x",
            let fixed_url = masternode_url.replace(":x", ":9093");
            let path = format!("http://{}/{}", fixed_url, self.force_stop_peer.clone());
            debug!("masternode_url={:?}", masternode_url);
            let body_clone = body.clone();
            
            // Spawn a new task for this request
            let handle = tokio::spawn(async move {
                match HTTP_CLIENT.post(path).json(&body_clone).send().await {
                    Ok(response) => {
                        debug!("response={:?}", response);
                        response.status() == StatusCode::OK
                    }
                    Err(e) => {
                        debug!("error request to masternode={:?}", e);
                        false
                    },
                }
            });
            
            handles.push(handle);
        }
        
        // Wait for all tasks and check results
        let mut any_success = false;
        for handle in handles {
            // Unwrap the JoinHandle result and get the inner boolean
            if let Ok(success) = handle.await {
                if success {
                    any_success = true;
                }
            }
        }
        
        if any_success {
            Ok(())
        } else {
            Err(Error::msg("Failed to force stop peer: all masternode requests failed"))
        }
    }

    async fn check_peer(self: Arc<Self>, algo_id_session: String) -> Option<(String,String)> {
        let start = std::time::Instant::now();
        
        // Get peer ip from id_session in redis concurrently
        let redis_svc = self.redis_svc_2.clone();
        
        // Get algo proxy info and calculate ip_bits concurrently
        let redis_start = std::time::Instant::now();
        let (redis_algo_proxy_info, ip_bits) = tokio::join!(
            async {
                let redis_svc = redis_svc.clone();
                redis_svc
                    .hget::<RedisAlgoProxyInfo>("algo_proxy".to_string(), algo_id_session.clone())
                    .await
                    .map_err(|e| anyhow!("failed to get algo proxy info from redis err={}", e))
                    .ok()
            },
            async {
                let redis_svc = redis_svc.clone();
                if let Some(info) = redis_svc
                    .hget::<RedisAlgoProxyInfo>("algo_proxy".to_string(), algo_id_session.clone())
                    .await
                    .ok()
                {
                    get_ip_bits_from_ip_address(info.peer_ip_addr).ok()
                } else {
                    None
                }
            }
        );
        info!("Redis operations took: {:?}", redis_start.elapsed());

        let redis_algo_proxy_info = redis_algo_proxy_info?;
        
        // let ip_bits = ip_bits?;
        
        // debug!("Checking peer with ip_bits={:?}", ip_bits);
        
        // // Check all masternodes concurrently
        // let masternode_check_start = std::time::Instant::now();
        // let masternode_arr = ["masternode0", "masternode1", "masternode2"];
        // let mut checks = Vec::new();
        
        // for masternode in masternode_arr {
        //     let (key, field) = DPNRedisKey::get_peers_kf(masternode.to_string(), ip_bits);
        //     let redis_svc = redis_svc.clone();
        //     checks.push(tokio::spawn(async move {
        //         match redis_svc.hget::<PeerChangedInfo>(key, field).await {
        //             Ok(_) => Some(masternode),
        //             Err(e) => {
        //                 debug!("Error checking Redis for peer in {}: {}", masternode, e);
        //                 None
        //             }
        //         }
        //     }));
        // }

        // // Wait for all checks to complete
        // let results = future::join_all(checks).await;
        // info!("Masternode checks took: {:?}", masternode_check_start.elapsed());
        
        // // Check if any masternode has the peer
        // let masternode_found = results.iter().any(|result| {
        //     if let Ok(Some(_)) = result {
        //         true
        //     } else {
        //         false
        //     }
        // });

        info!("Total check_peer execution time: {:?}", start.elapsed());
        Some((redis_algo_proxy_info.peer_ip_addr,redis_algo_proxy_info.masternode_ip))
        // if masternode_found {
        //     Some(redis_algo_proxy_info.peer_ip_addr)
        // } else {
        //     None
        // }
    }
}

impl BootnodeServiceImpl {
    pub fn new(conn_svc: Arc<dyn ConnectionService>, redis_svc: Arc<RedisService>, redis_svc_2: Arc<RedisService2>) -> Self {
        Self {
            registered_masternodes: Arc::new(RwLock::new(HashMap::new())),
            conn_svc,
            online_peers: "masternode/peers".to_string(),
            masternode_metrics: "metrics/overview".to_string(),
            force_stop_peer: "masternode/algo/peers/force_close".to_string(),
            masternode_country_code_map: DashMap::new(),
            registered_masternodes_clone: DashMap::new(),
            redis_svc,
            redis_svc_2,
        }
    }

    pub async fn load_masternode_country_code_map(self: Arc<Self>) -> Result<(), Error> {
        debug!(
            "masternode country code file path: {}",
            APP_CONFIG.masternode_country_code_file_path
        );

        let file = File::open(APP_CONFIG.masternode_country_code_file_path.clone())?;
        let reader = BufReader::new(file);

        // Parse as generic JSON Values
        let json_data: Vec<serde_json::Value> = serde_json::from_reader(reader)?;

        // Create a HashMap from the parsed data
        let mut map = HashMap::new();
        for entry in json_data.iter() {
            // Safely extract geoname_id and masternode_id if they exist
            let geoname_id = entry.get("geoname_id").and_then(|v| v.as_i64());
            let ip = entry.get("ip").and_then(|v| v.as_str());
            // Only process entries that have all required fields
            if let (Some(geoname_id), Some(ip)) = (geoname_id, ip) {
                map.insert(geoname_id, ip.to_string());
            }
        }

        debug!(
            "Created masternode country code map with {} entries",
            map.len()
        );

        // Update your map with the new data
        self.masternode_country_code_map.clear();
        for (k, v) in map {
            self.masternode_country_code_map.insert(k, v);
        }

        Ok(())
    }
}
