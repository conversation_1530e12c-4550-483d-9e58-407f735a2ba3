pub mod store;

use crate::bootnode::utils::get_geo_from_ip_address;
use crate::integration::masternode_client::{MasternodeAlgoProxyReq, MasternodeClientService, MasternodeProxyHealthCheckResp};
use crate::monitoring::profiling::CpuProfiler;
use crate::user::UserService;
use crate::utils::iso_to_geoname_id::IsoToGeonameIdService;
use crate::webapi::clients::{AlgoProxyRequest, AlgoProxyResponse};
use crate::webapi::connections::BandwidthPrice;
use anyhow::{anyhow, Error, Result};
use async_trait::async_trait;
use dashmap::DashMap;
use dpn_core::services::redis::{DPNRedisKey, RedisService};
use dpn_core::services::types::ProxyAccChanged;
use dpn_core::{
    types::{
        bandwidth::Session,
        connection::{PeernodeInfo, PrioritizedIPLevel, ProxyAccData, VerifyProxyAccData},
        geo::Geo,
        msg_queue::{SessionCreatedExtra, SessionTerminatedExtra},
    },
    utils::{bytes_to_hex_string, hash::hash, szabo_to_u256},
};
use ethers::types::TxHash;
use ethers::types::U256;
use log::{debug, error, info};
use mockall::automock;
use rand::Rng;
use serde::{Deserialize, Serialize};
use sqlx::types::chrono::Utc;
use std::sync::Arc;
use std::{fmt::Debug, iter};
use tokio::sync::{
    mpsc::{UnboundedReceiver, UnboundedSender},
    Mutex,
};

use self::store::ConnectionStorage;
use std::collections::HashMap;
use std::time::Instant;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisAlgoProxyInfo {
    pub masternode_ip: String,
    pub peer_ip_addr: String,
    pub created_at: i64,
}

#[automock]
#[async_trait]
pub trait ConnectionService: Debug + Send + Sync + 'static {
    // sessions
    async fn get_sessions_by_provider(
        self: Arc<Self>,
        provider_addr: String,
    ) -> anyhow::Result<Vec<Session>>;
    async fn get_session_by_id(self: Arc<Self>, session_hash: String) -> Result<Option<Session>>;
    async fn on_session_created(self: Arc<Self>, extra: SessionCreatedExtra) -> Result<(), Error>;
    async fn on_session_terminated(
        self: Arc<Self>,
        extra: SessionTerminatedExtra,
    ) -> Result<(), Error>;

    // peers
    async fn on_peer_connected(
        self: Arc<Self>,
        peer_info: PeernodeInfo,
        masternode_id: String,
        login_session_id: String,
    ) -> Result<()>;
    async fn on_peer_disconnected(self: Arc<Self>, peer_addr: String) -> Result<()>;

    // master nodes
    async fn on_masternode_deregistered(self: Arc<Self>) -> Result<()>;

    // proxy accounts
    async fn create_proxy_acc(
        self: Arc<Self>,
        user_addr: String,
        country_geoname_id: i64,
        rate_per_kb: i64,
        rate_per_second: i64,
        whitelisted_ip: Option<String>,
        prioritized_ip: Option<String>,
        prioritized_ip_level: Option<PrioritizedIPLevel>,
        ip_rotation_period: i64,
    ) -> Result<ProxyAccData>;
    async fn verify_proxy_acc(
        self: Arc<Self>,
        verify_proxy_acc: VerifyProxyAccData,
    ) -> Result<ProxyAccData>;

    // algo proxies
    async fn create_algo_proxy(
        self: Arc<Self>,
        user_addr: String,
        algo_proxy_req: AlgoProxyRequest,
        masternode_map_lock: DashMap<i64, String>,
    ) -> Result<AlgoProxyResponse>;

    async fn delete_algo_proxy(
        self: Arc<Self>,
        user_addr: &str,
        algo_id_session: &str,
    ) -> Result<String>;

    async fn health_check_proxy(
        self: Arc<Self>,
        algo_id_session: &str,
        masternode_ip: &str,
    ) -> Result<MasternodeProxyHealthCheckResp, Error>;

    async fn get_suggested_bandwidth_price(self: Arc<Self>) -> BandwidthPrice;
    async fn create_region_info_history(
        self: Arc<Self>,
        region_id: i64,
        is_country: bool,
        name: String,
        country_geoname_id: Option<i64>,
        country_geoname_name: Option<String>,
    ) -> anyhow::Result<()>;

}

#[derive(Debug)]
pub struct ConnectionServiceImpl {
    user_svc: Arc<dyn UserService>,
    redis_svc: Arc<RedisService>,
    store: Arc<ConnectionStorage>,
    iso_to_geoname_id_svc: Arc<IsoToGeonameIdService>,
    ms_client_svc: Arc<dyn MasternodeClientService>,

    shutdown_tx: UnboundedSender<()>,
    shutdown_rx: Mutex<UnboundedReceiver<()>>,
}

#[async_trait]
impl ConnectionService for ConnectionServiceImpl {
    async fn get_sessions_by_provider(
        self: Arc<Self>,
        provider_addr: String,
    ) -> Result<Vec<Session>> {
        self.clone()
            .store
            .clone()
            .get_sessions_by_provider(provider_addr)
            .await
    }

    async fn get_session_by_id(self: Arc<Self>, session_hash: String) -> Result<Option<Session>> {
        self.clone()
            .store
            .clone()
            .get_session_by_id(session_hash)
            .await
    }

    async fn create_algo_proxy(
        self: Arc<Self>,
        user_addr: String,
        algo_proxy_req: AlgoProxyRequest,
        masternode_map_lock: DashMap<i64, String>,
    ) -> Result<AlgoProxyResponse> {
        // 1. Call masternode client to create algo proxy
        let iso_code: &str = &algo_proxy_req.country_iso_code;
        let country_geoname_id = self
            .iso_to_geoname_id_svc
            .clone()
            .iso_code_to_geoname_id_complete(iso_code);
        if country_geoname_id.is_none() {
            return Err(anyhow!(
                "failed to get country_geoname_id for iso_code: {}",
                iso_code
            ));
        }
        let country_geoname_id = country_geoname_id.unwrap() as i64;

        // map country_geoname_id to masternode_ip
        let masternode_ip = masternode_map_lock
        .get(&country_geoname_id)
        .map(|v| v.value().clone())
        .unwrap_or_else(|| "".to_string());
 
        info!(
            "found masternode_ip: {} , by geo_id: {}",
            masternode_ip,
            country_geoname_id
        );


        let ms_algo_proxy_req = MasternodeAlgoProxyReq {
            client_addr: user_addr.clone(),
            country_geoname_id: country_geoname_id as i64,
            algo_id_session: algo_proxy_req.algo_id_session.clone(),
        };
        info!("ms_algo_proxy_req: {:?}", ms_algo_proxy_req);
        let ms_returned_proxy = self
            .ms_client_svc
            .clone()
            .create_algo_proxy(ms_algo_proxy_req, masternode_ip.clone())
            .await?;
        info!("ms_returned_proxy: {:?}", ms_returned_proxy.username);
        // Check if the proxy is already created
        if ms_returned_proxy.username == "" {
            return Err(anyhow!(
                "There is no peer from masternode that matched the request"
            ));
        }
        let ip_rotation_period = 3600;
        let rate_per_kb = 100;

        let mut _proxy_acc = ProxyAccData {
            id: ms_returned_proxy.username.to_string(),
            password: ms_returned_proxy.password.to_string(),
            ip_rotation_period,
            whitelisted_ip: None,
            user_addr,
            country_geoname_id,
            city_geoname_id: Some(ms_returned_proxy.city_geo_name_id),
            rate_per_kb,
            rate_per_second: 0,
            prioritized_ip: None,
            prioritized_ip_level: None,
            created_at: Utc::now().timestamp(),
        };

        // 2. store the proxy acc in redis
        let _ = self.redis_svc.clone().hset(
            "algo_proxy".to_string(),
            algo_proxy_req.algo_id_session.clone(),
            RedisAlgoProxyInfo {
                masternode_ip: ms_returned_proxy.proxy_ip.clone(),
                peer_ip_addr: ms_returned_proxy.peer_ip_addr.clone(),
                created_at: Utc::now().timestamp(),
            },
        )?;

        // 3. Save it to DB, return algo proxy info
        let _ = self.store.clone().create_proxy_acc(_proxy_acc).await?;
        Ok(AlgoProxyResponse {
            country_iso_code: iso_code.to_string(),
            algo_session_id: algo_proxy_req.algo_id_session,
            username: ms_returned_proxy.username,
            password: ms_returned_proxy.password,
            proxy_ip: ms_returned_proxy.proxy_ip,
            proxy_port: ms_returned_proxy.proxy_port,
            provider_public_ip_address: ms_returned_proxy.peer_ip_addr,
        })
    }

    async fn delete_algo_proxy(
        self: Arc<Self>,
        user_addr: &str,
        algo_id_session: &str,
    ) -> Result<String> {
        // 1. First, check if the proxy account with this algo_id_session exists in Redis
        let redis_algo_proxy_info = self
            .redis_svc
            .clone()
            .hget::<RedisAlgoProxyInfo>("algo_proxy".to_string(), algo_id_session.to_string())
            .map_err(|e| anyhow!("failed to get algo proxy info from redis err={}", e))?;
        info!("redis_algo_proxy_info: {:?}", redis_algo_proxy_info.clone());

        // 2. Call masternode client to delete the algo proxy
        let algo_id_in_ms: String = format!("algo_{}", algo_id_session);
        let masternode_ip = redis_algo_proxy_info.masternode_ip;
        let _ = self
            .ms_client_svc
            .clone()
            .delete_algo_proxy(&algo_id_in_ms, &masternode_ip)
            .await
            .map_err(|e| {
                anyhow!(
                    "failed to delete algo proxy from masternode client err={}",
                    e
                )
            })?;
        info!("deleted algo proxy from masternode client");

        // 3. Delete the proxy account from Redis
        let _ = self
            .redis_svc
            .clone()
            .hdel("algo_proxy".to_string(), algo_id_session.to_string())
            .map_err(|e| anyhow!("failed to delete algo proxy from redis err={}", e))?;
        info!("deleted algo proxy from redis");

        // 4. Delete the proxy account from DB
        let _ = self
            .clone()
            .store
            .clone()
            .delete_proxy_acc(user_addr, &algo_id_in_ms)
            .await
            .map_err(|e| anyhow!("failed to delete algo proxy from db err={}", e))?;
        info!("deleted algo proxy from db");
        return Ok(algo_id_session.to_string());
    }

    async fn health_check_proxy(
        self: Arc<Self>,
        algo_id_session: &str,
        masternode_ip: &str,
    ) -> Result<MasternodeProxyHealthCheckResp, Error> {
        // 1. First, check if the proxy account with this algo_id_session exists in Redis
        let start_time = Instant::now();
        // let redis_algo_proxy_info = self
        //     .redis_svc
        //     .clone()
        //     .hget::<RedisAlgoProxyInfo>("algo_proxy".to_string(), algo_id_session.to_string())
        //     .map_err(|e| anyhow!("failed to get algo proxy info from redis err={}", e))?;
        // info!("redis_algo_proxy_info: {:?}, duration: {:?}", redis_algo_proxy_info.clone(), start_time.elapsed());

        // // 2. Call masternode client for health check
        // let masternode_ip = redis_algo_proxy_info.masternode_ip;
        let result =  self
            .ms_client_svc
            .clone()
            .health_check_proxy(algo_id_session, masternode_ip)
            .await;
        info!("Heath check proxy through master node duration: {:?} and session_id: {:?}", start_time.elapsed(), algo_id_session);
        return result;

    }

    async fn on_session_created(self: Arc<Self>, extra: SessionCreatedExtra) -> Result<(), Error> {
        let store = self.store.clone();
        // TODO: check if client_addr and peer_addr are valid or not
        if let Err(e) = store.create_session(extra).await {
            error!("{}", e);
        }
        Ok(())
    }

    async fn on_session_terminated(
        self: Arc<Self>,
        extra: SessionTerminatedExtra,
    ) -> Result<(), Error> {
        let _profiler = CpuProfiler::new("on_session_terminated", "ConnectionService");
        _ = self
            .user_svc
            .clone()
            .add_user_tier_points(
                extra.session.peer_addr.clone(),
                extra.session.bandwidth_usage as i64,
            )
            .await;

        // TODO(rameight): apply duration fee?
        let duration_fee = U256::zero();
        let bandwidth_fee =
            szabo_to_u256((extra.session.bandwidth_usage * extra.session.rate_per_kb) as i64);
        let total_fee = duration_fee + bandwidth_fee;

        let _ = self
            .clone()
            .store
            .clone()
            .modify_session(
                extra.clone(),
                TxHash::zero(),
                duration_fee,
                bandwidth_fee,
                total_fee,
                Some(U256::zero()),
            )
            .await
            .map_err(|e| {
                anyhow!(
                    "modify session failed err={} session={:#?}",
                    e,
                    extra.session.clone()
                )
            })?;
        Ok(())
    }

    async fn create_proxy_acc(
        self: Arc<Self>,
        user_addr: String,
        country_geoname_id: i64,
        rate_per_kb: i64,
        rate_per_second: i64,
        whitelisted_ip: Option<String>,
        prioritized_ip: Option<String>,
        prioritized_ip_level: Option<PrioritizedIPLevel>,
        ip_rotation_period: i64,
    ) -> Result<ProxyAccData> {
        // a proxy acc with temp password will be generated
        let temp_pwd = generate_password();
        let proxy_acc = ProxyAccData::new(
            temp_pwd,
            ip_rotation_period,
            whitelisted_ip,
            user_addr,
            country_geoname_id,
            None,
            rate_per_kb,
            rate_per_second,
            prioritized_ip,
            prioritized_ip_level,
            Utc::now().timestamp(),
        );

        match self.store.clone().create_proxy_acc(proxy_acc).await {
            Ok(storage_proxy_acc) => {
                // hides password
                let mut proxy_acc: ProxyAccData = storage_proxy_acc.clone().into();
                proxy_acc.password =
                    bytes_to_hex_string(hash(proxy_acc.password.as_bytes()).as_bytes());

                let changed = ProxyAccChanged::Created(proxy_acc);
                if let Err(e) = self
                    .clone()
                    .redis_svc
                    .clone()
                    .publish_proxy_acc(changed.clone())
                    .await
                {
                    error!("failed to publish proxy acc={:?} err={}", changed, e);
                }
                return Ok(storage_proxy_acc.into());
            }
            Err(e) => return Err(anyhow!("create proxy acc err: {}", e)),
        }
    }

    async fn verify_proxy_acc(
        self: Arc<Self>,
        verify_proxy_acc: VerifyProxyAccData,
    ) -> Result<ProxyAccData> {
        let proxy_acc = match verify_proxy_acc.clone() {
            VerifyProxyAccData::IP(ip) => {
                match self.store.clone().get_proxy_acc_by_ip(ip.clone()).await {
                    Ok(proxy_acc) => Some(proxy_acc),
                    Err(_) => None,
                }
            }
            VerifyProxyAccData::BasicAuth(proxy_acc_id, _) => {
                match self
                    .store
                    .clone()
                    .get_proxy_acc_by_id(proxy_acc_id.clone())
                    .await
                {
                    Ok(proxy_acc) => Some(proxy_acc),
                    Err(_) => None,
                }
            }
        };

        match proxy_acc {
            Some(proxy_acc_data) => {
                let mut proxy_acc_data: ProxyAccData = proxy_acc_data.into();
                match verify_proxy_acc.clone() {
                    VerifyProxyAccData::BasicAuth(username, password) => {
                        if password != proxy_acc_data.password {
                            return Err(anyhow!("invalid proxy acc: username={}", username));
                        }
                    }
                    _ => {}
                }

                // hides password
                proxy_acc_data.password =
                    bytes_to_hex_string(hash(proxy_acc_data.password.as_bytes()).as_bytes());
                return Ok(proxy_acc_data);
            }
            None => {
                let identifer = match verify_proxy_acc {
                    VerifyProxyAccData::IP(ip) => ip.clone(),
                    VerifyProxyAccData::BasicAuth(username, _) => username.clone(),
                };
                return Err(anyhow!("invalid proxy acc: {}", identifer));
            }
        }
    }

    async fn get_suggested_bandwidth_price(self: Arc<Self>) -> BandwidthPrice {
        // TODO(rameight): a mechanism to update suggested bandwidth price is needed
        // this function should returns from the network's suggested bandwidth price
        // hardcode for now

        // 1 U2U/GB = 1M szabo/1M kb = 1 szabo/kb
        let suggested_rate_per_kb = 50;
        // 1 U2U/h = 1M szabo/3600s = 278 szabo/s
        // let suggested_rate_per_second = 278;
        // TODO(rameight): hardcode here cause we don't charge users duration usage.
        let suggested_rate_per_second = 0;

        BandwidthPrice {
            rate_per_kb: suggested_rate_per_kb,
            rate_per_second: suggested_rate_per_second,
        }
    }

    async fn on_masternode_deregistered(self: Arc<Self>) -> Result<()> {
        _ = self
            .store
            .clone()
            .stop_all_connection_history_of_masternode()
            .await;

        Ok(())
    }

    async fn on_peer_connected(
        self: Arc<Self>,
        peer_info: PeernodeInfo,
        masternode_id: String,
        login_session_id: String,
    ) -> Result<()> {
        info!("bravo! peer connected: {}", peer_info.peer_id);

        let geo: Geo = match get_geo_from_ip_address(peer_info.ip_addr.clone()) {
            Ok(geo) => geo,
            Err(_) => Geo::default(),
        };

        let (k, f) = DPNRedisKey::get_geo_kf(masternode_id.clone(), login_session_id.clone());
        if let Err(e) = self.clone().redis_svc.clone().hset(k, f, geo) {
            error!("failed to set geo err={}", e)
        };

        match self
            .clone()
            .user_svc
            .clone()
            .get_bandwidth_price_of(peer_info.peer_id.clone())
            .await
        {
            Ok(price) => {
                if let Err(e) = self
                    .clone()
                    .redis_svc
                    .clone()
                    .publish_peer_price(price.clone())
                    .await
                {
                    error!("failed to publish peer price price={:?} err={}", price, e);
                }
            }
            Err(e) => error!("failed to get price err={}", e),
        }

        let store = self.store.clone();
        match store
            .create_connection_history(
                Utc::now().timestamp() as i32,
                peer_info.peer_id,
                peer_info.city_geoname_id,
                peer_info.country_geoname_id,
                get_ip_address(peer_info.ip_addr.clone()),
                masternode_id,
                login_session_id,
            )
            .await
        {
            Ok(_) => return Ok(()),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn on_peer_disconnected(self: Arc<Self>, peer_addr: String) -> Result<()> {
        info!("brova! peer disconnected: {}", peer_addr);

        let store = self.store.clone();
        match store
            .clone()
            .stop_latest_connection_history(peer_addr, Utc::now().timestamp() as i32)
            .await
        {
            Ok(_) => return Ok(()),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn create_region_info_history(
        self: Arc<Self>,
        region_id: i64,
        is_country: bool,
        name: String,
        country_geoname_id: Option<i64>,
        country_geoname_name: Option<String>,
    ) -> anyhow::Result<()> {
        match self
            .store
            .clone()
            .create_region_info_history(
                region_id,
                is_country,
                name,
                country_geoname_id,
                country_geoname_name,
            )
            .await
        {
            Ok(_) => Ok(()),
            Err(e) => return Err(anyhow!("{}", e)),
        }
    }

}

impl ConnectionServiceImpl {
    pub async fn new(
        connection_store: Arc<ConnectionStorage>,
        user_svc: Arc<dyn UserService>,
        redis_svc: Arc<RedisService>,
        iso_to_geoname_id_svc: Arc<IsoToGeonameIdService>,
        ms_client_svc: Arc<dyn MasternodeClientService>,
    ) -> Result<Self, Error> {
        let (shutdown_tx, shutdown_rx) = tokio::sync::mpsc::unbounded_channel::<()>();

        Ok(Self {
            shutdown_tx,
            shutdown_rx: Mutex::new(shutdown_rx),
            store: connection_store,
            user_svc: user_svc.clone(),
            redis_svc: redis_svc.clone(),
            iso_to_geoname_id_svc: iso_to_geoname_id_svc.clone(),
            ms_client_svc: ms_client_svc.clone(),
        })
    }

    pub async fn run(self: Arc<Self>) -> Result<(), Error> {
        let _self = self.clone();
        let _self2 = self.clone();
        let mut shutdown_rx = _self2.shutdown_rx.lock().await;

        if let Err(e) = self.clone().redis_svc.clone().remove_all_proxy_accs().await {
            return Err(anyhow!(
                "failed to remove proxy accs from last run err={}",
                e
            ));
        }

        match self.clone().upload_proxy_accs().await {
            Ok(total) => {
                info!("redis: uploaded proxy accs total={}", total);
            }
            Err(e) => {
                error!("redis: upload proxy accs failed err={}", e);
                return Err(anyhow!("redis: upload proxy accs failed err={}", e));
            }
        };

        if let Err(e) = self
            .redis_svc
            .clone()
            .publish_proxy_acc(ProxyAccChanged::RefreshAll())
            .await
        {
            return Err(anyhow!("failed publish proxy refresh all event err={}", e));
        }

        tokio::select! {
            _ =  shutdown_rx.recv() => {
                info!("service stopped");
                Ok(())
            },
        }
    }

    pub async fn stop(self: Arc<Self>) -> Result<()> {
        info!("shutting down service...");
        let _self = self.clone();
        let _self2 = self.clone();
        _ = _self.shutdown_tx.send(());
        Ok(())
    }

    async fn upload_proxy_accs(self: Arc<Self>) -> Result<usize, Error> {
        let proxy_accs = self
            .clone()
            .store
            .clone()
            .get_all_proxy_accounts()
            .await
            .map_err(|e| anyhow!("failed to get proxy accs err={}", e))?;

        for proxy_acc in proxy_accs.clone() {
            // hides password
            let mut proxy_acc = proxy_acc.clone();
            proxy_acc.password =
                bytes_to_hex_string(hash(proxy_acc.password.as_bytes()).as_bytes());

            let changed = ProxyAccChanged::Created(proxy_acc.into());
            self.clone()
                .redis_svc
                .clone()
                .publish_proxy_acc(changed.clone())
                .await
                .map_err(|e| {
                    anyhow!(
                        "failed to upload proxy acc changed={:?} err={}",
                        changed.clone(),
                        e
                    )
                })?;
        }
        Ok(proxy_accs.len())
    }

}

fn generate_password() -> String {
    const PROXY_ACC_PASSWORD_LENGTH: usize = 12;
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    let password: String = iter::repeat_with(|| {
        let index = rand::thread_rng().gen_range(0..CHARSET.len());
        CHARSET[index] as char
    })
    .take(PROXY_ACC_PASSWORD_LENGTH)
    .collect();

    password
}

fn get_ip_address(address: String) -> String {
    if address.len() >= 20 {
        let parts: Vec<&str> = address.split(':').collect();
        parts[0].to_string()
    } else {
        address
    }
}
