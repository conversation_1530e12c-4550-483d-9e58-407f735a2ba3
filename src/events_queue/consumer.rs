use anyhow::{Error, Result};
use async_trait::async_trait;
use dpn_core::types::msg_queue::{
        DPNEvent, CONNECTION_EVENTS_ADMIN_QUEUE, SESSION_EVENTS_ADMIN_QUEUE,
    };
use futures_lite::StreamExt;
use lapin::{
    options::{BasicAckOptions, BasicConsumeOptions, BasicNackOptions},
    types::FieldTable,
    Connection, ConnectionProperties,
};
use log::{error, info};
use mockall::automock;
use std::{fmt::Debug, sync::Arc, time::Duration};
use tokio::sync::{
    mpsc::{UnboundedReceiver, UnboundedSender},
    Mutex,
    Semaphore,
};

use crate::{connection::ConnectionService, APP_CONFIG};

#[automock]
#[async_trait]
pub trait ConsumerService: Debug + Send + Sync + 'static {
    async fn stop(self: Arc<Self>) -> Result<()>;
    async fn run(self: Arc<Self>) -> Result<(), Error>;
}

#[derive(Debug)]
pub struct ConsumerServiceImpl {
    conn_svc: Arc<dyn ConnectionService>,
    shutdown_tx: UnboundedSender<()>,
    shutdown_rx: Mutex<UnboundedReceiver<()>>,
    task_semaphore: Arc<tokio::sync::Semaphore>,
}

impl ConsumerServiceImpl {
    pub async fn new(
        conn_svc: Arc<dyn ConnectionService>
    ) -> Result<Self, Error> {
        let (shutdown_tx, shutdown_rx) = tokio::sync::mpsc::unbounded_channel::<()>();
        
        // Create a semaphore with 16 permits
        let task_semaphore: Arc<Semaphore> = Arc::new(tokio::sync::Semaphore::new(16));

        Ok(Self {
            conn_svc,
            shutdown_tx,
            shutdown_rx: Mutex::new(shutdown_rx),
            task_semaphore,
        })
    }

    pub async fn run(self: Arc<Self>) -> Result<(), Error> {
        let _self = self.clone();
        let _self2 = self.clone();
        let mut shutdown_rx = _self2.shutdown_rx.lock().await;
        tokio::select! {
            _ =  shutdown_rx.recv() => {
                info!("consumer service stopped");
                Ok(())
            },
            Err(e) = _self.clone().process_peer_connection_events() => {
                error!("process_peer_connection_events error: {}", e);
                Err(e)
            }
            Err(e) = _self.clone().process_session_events() => {
                error!("process_session_events error: {}", e);
                Err(e)
            }
            // Err(e) = _self.clone().process_tappoint_events() => {
            //     error!("process_tappoint_events error: {}", e);
            //     Err(e)
            // }
        }
    }

    pub async fn stop(self: Arc<Self>) -> Result<()> {
        info!("shutting down consumer service...");
        _ = self.shutdown_tx.send(());
        Ok(())
    }

    async fn process_peer_connection_events(self: Arc<Self>) -> Result<(), Error> {
        let mut loop_count = 0;
        loop {
            loop_count += 1;
            info!("Starting connection attempt #{} to RabbitMQ for peer events", loop_count);
            
            match Connection::connect(&APP_CONFIG.rmq_uri, ConnectionProperties::default()).await {
                Ok(conn) => match conn.create_channel().await {
                    Ok(channel) => {
                        info!("Successfully connected to RabbitMQ and created channel for peer events");
                        let mut consumer = match channel
                            .basic_consume(
                                CONNECTION_EVENTS_ADMIN_QUEUE,
                                "",
                                BasicConsumeOptions::default(),
                                FieldTable::default(),
                            )
                            .await
                        {
                            Ok(consumer) => consumer,
                            Err(e) => {
                                error!("cannot create connection events consumer err={}", e);
                                tokio::time::sleep(Duration::from_secs(2)).await; // Added longer backoff
                                continue;
                            }
                        };
                        
                        info!("Waiting for peer connection events from RabbitMQ...");
                        let mut message_count = 0;
                        let mut empty_count = 0;
                        
                        while let Some(delivery) = consumer.next().await {
                            message_count += 1;
                            empty_count = 0; // Reset empty counter when we get a message
                            
                            if message_count % 100 == 0 {
                                info!("Processed {} peer connection messages so far", message_count);
                            }
                            
                            let _self = self.clone();
                            match delivery {
                                Ok(delivery) => {
                                    // Acquire a permit from the semaphore before spawning a task
                                    let permit = self.task_semaphore.clone().acquire_owned().await.unwrap();
                                    tokio::spawn(async move {
                                        let payload = String::from_utf8_lossy(&delivery.data);
                                        if let Ok(event) =
                                            serde_json::from_str::<DPNEvent>(&payload)
                                        {
                                            match event {
                                                DPNEvent::PeerConnected(extra) => {
                                                    match _self
                                                        .clone()
                                                        .conn_svc
                                                        .clone()
                                                        .on_peer_connected(
                                                            extra.info,
                                                            extra.masternode_id,
                                                            extra.login_session_id,
                                                        )
                                                        .await
                                                    {
                                                        Ok(_) => {
                                                            _ = delivery
                                                                .ack(BasicAckOptions::default())
                                                                .await;
                                                        }
                                                        Err(e) => {
                                                            _ = delivery
                                                                .nack(BasicNackOptions::default())
                                                                .await;
                                                            error!("handle event failed err={}", e);
                                                        }
                                                    }
                                                    drop(permit); // Release the permit when done
                                                    return;
                                                }
                                                DPNEvent::PeerDisconnected(extra) => {
                                                    match _self
                                                        .clone()
                                                        .conn_svc
                                                        .clone()
                                                        .on_peer_disconnected(extra.peer_addr)
                                                        .await
                                                    {
                                                        Ok(_) => {
                                                            _ = delivery
                                                                .ack(BasicAckOptions::default())
                                                                .await;
                                                        }
                                                        Err(e) => {
                                                            _ = delivery
                                                                .nack(BasicNackOptions::default())
                                                                .await;
                                                            error!("handle event failed err={}", e);
                                                        }
                                                    }
                                                    drop(permit); // Release the permit when done
                                                    return;
                                                }
                                                _ => {
                                                    error!("unsupported event={:#?}", event)
                                                }
                                            };
                                        } else {
                                            error!(
                                                "failed to decode connection_event: {:#?}",
                                                payload
                                            );
                                        }
                                        if let Err(e) =
                                            delivery.ack(BasicAckOptions::default()).await
                                        {
                                            error!(
                                                "error acknowledging message: err={} msg={}",
                                                e, payload
                                            );
                                        }
                                        tokio::time::sleep(Duration::from_millis(100)).await;
                                        drop(permit); // Release the permit when done
                                    });
                                }
                                Err(e) => {
                                    error!("error receiving message err={}", e);
                                    // Add a small sleep 
                                    tokio::time::sleep(Duration::from_millis(100)).await;
                                }
                            }
                        }
                        
                        // the consumer.next() returned None - the stream ended
                        empty_count += 1;
                        error!("RabbitMQ peer connection consumer stream ended (empty_count={})", empty_count);
                        
                        // Add a sleep to prevent tight loop if the stream keeps ending immediately
                        tokio::time::sleep(Duration::from_secs(2)).await;
                    }
                    Err(e) => {
                        error!("cannot create channel for events queue err={}", e);
                        tokio::time::sleep(Duration::from_secs(2)).await; // Added longer backoff
                        continue;
                    }
                },
                Err(e) => {
                    error!("cannot connect to rabbitmq, err={}", e);
                    tokio::time::sleep(std::time::Duration::from_secs(5)).await; // Increased backoff

                    continue;
                }
            }
            
            info!("Exited RabbitMQ consumer loop for peer events, will reconnect");
            tokio::time::sleep(Duration::from_secs(1)).await; // Safety sleep before reconnecting
            continue;
        }
    }

    async fn process_session_events(self: Arc<Self>) -> Result<(), Error> {
        let mut loop_count = 0;
        loop {
            loop_count += 1;
            info!("Starting connection attempt #{} to RabbitMQ for session events", loop_count);
            
            match Connection::connect(&APP_CONFIG.rmq_uri, ConnectionProperties::default()).await {
                Ok(conn) => match conn.create_channel().await {
                    Ok(channel) => {
                        info!("Successfully connected to RabbitMQ and created channel for session events");
                        let mut consumer = match channel
                            .basic_consume(
                                SESSION_EVENTS_ADMIN_QUEUE,
                                "",
                                BasicConsumeOptions::default(),
                                FieldTable::default(),
                            )
                            .await
                        {
                            Ok(consumer) => consumer,
                            Err(e) => {
                                error!("cannot create session events consumer err={}", e);
                                tokio::time::sleep(Duration::from_secs(2)).await; // Added longer backoff
                                continue;
                            }
                        };
                        
                        info!("Waiting for session events from RabbitMQ...");
                        let mut message_count = 0;
                        let mut empty_count = 0;
                        
                        while let Some(delivery) = consumer.next().await {
                            message_count += 1;
                            empty_count = 0; // Reset empty counter when we get a message
                            
                            if message_count % 100 == 0 {
                                info!("Processed {} session messages so far", message_count);
                            }
                            
                            let _self = self.clone();
                            match delivery {
                                Ok(delivery) => {
                                    // Acquire a permit from the semaphore before spawning a task
                                    let permit = self.task_semaphore.clone().acquire_owned().await.unwrap();
                                    tokio::spawn(async move {
                                        let payload = String::from_utf8_lossy(&delivery.data);
                                        if let Ok(event) =
                                            serde_json::from_str::<DPNEvent>(&payload)
                                        {
                                            match event {
                                                DPNEvent::SessionCreated(extra) => {
                                                    match _self
                                                        .clone()
                                                        .conn_svc
                                                        .clone()
                                                        .on_session_created(extra)
                                                        .await
                                                    {
                                                        Ok(_) => {
                                                            _ = delivery
                                                                .ack(BasicAckOptions::default())
                                                                .await;
                                                        }
                                                        Err(e) => {
                                                            _ = delivery
                                                                .nack(BasicNackOptions::default())
                                                                .await;
                                                            error!(
                                                                "create session failed err={}",
                                                                e
                                                            );
                                                        }
                                                    }
                                                    drop(permit); // Release the permit when done
                                                    return;
                                                }
                                                DPNEvent::SessionTerminated(extra) => {
                                                    match _self
                                                        .clone()
                                                        .conn_svc
                                                        .clone()
                                                        .on_session_terminated(extra)
                                                        .await
                                                    {
                                                        Ok(_) => {
                                                            _ = delivery
                                                                .ack(BasicAckOptions::default())
                                                                .await;
                                                        }
                                                        Err(e) => {
                                                            _ = delivery
                                                                .nack(BasicNackOptions::default())
                                                                .await;
                                                            error!(
                                                                "terminate session failed err={}",
                                                                e
                                                            );
                                                        }
                                                    }
                                                    drop(permit); // Release the permit when done
                                                    return;
                                                }
                                                _ => {
                                                    error!("unsupported event={:#?}", event)
                                                }
                                            };
                                        } else {
                                            // error!(
                                            //     "failed to decode session_event: {:#?}",
                                            //     payload
                                            // );
                                        }
                                        if let Err(e) =
                                            delivery.ack(BasicAckOptions::default()).await
                                        {
                                            error!(
                                                "error acknowledging message: err={} msg={}",
                                                e, payload
                                            );
                                        }
                                        tokio::time::sleep(Duration::from_millis(100)).await;
                                        drop(permit); // Release the permit when done
                                    });
                                }
                                Err(e) => {
                                    error!("error receiving message err={}", e);
                                    // Add a small sleep on error to prevent tight loop
                                    tokio::time::sleep(Duration::from_millis(100)).await;
                                }
                            }
                        }
                        
                        // If we get here, the consumer.next() returned None - the stream ended
                        empty_count += 1;
                        error!("RabbitMQ session consumer stream ended (empty_count={})", empty_count);
                        
                        // Add a sleep to prevent tight loop if the stream keeps ending immediately
                        tokio::time::sleep(Duration::from_secs(2)).await;
                    }
                    Err(e) => {
                        error!("cannot create channel for session queue err={}", e);
                        tokio::time::sleep(Duration::from_secs(2)).await; // Added longer backoff
                        continue;
                    }
                },
                Err(e) => {
                    error!("cannot connect to RabbitMQ, err={}", e);
                    tokio::time::sleep(Duration::from_secs(5)).await; // Increased backoff
                    continue;
                }
            }
            
            info!("Exited RabbitMQ consumer loop for session events, will reconnect");
            tokio::time::sleep(Duration::from_secs(1)).await; // Safety sleep before reconnecting
            continue;
        }
    }

}
