use anyhow::{Error, Result};
use async_trait::async_trait;
use dpn_core::types::msg_queue::{
    DPNEvent, NotificationEvent, CONNECTION_ROUTING_KEY, DEPOSIT_ROUTING_KEY, EVENTS_EXCHANGE,
    NOTIFICATION_EXCHANGE, NOTIFICATION_REGISTER_ROUTING_KEY, REFERRAL_ROUTING_KEY,
    SESSION_ROUTING_KEY, WITHDRAWAL_ROUTING_KEY,
};
use lapin::options::BasicPublishOptions;
use lapin::{BasicProperties, Connection, ConnectionProperties};
use log::{info, warn};
use mockall::automock;
use std::{fmt::Debug, sync::Arc};
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};
use tokio::sync::Mutex;

use crate::APP_CONFIG;

#[automock]
#[async_trait]
pub trait PublisherService: Debug + Send + Sync + 'static {
    async fn stop(self: Arc<Self>) -> Result<()>;
    async fn run(self: Arc<Self>) -> Result<(), Error>;
    async fn publish_dpn_event(self: Arc<Self>, event: DPNEvent) -> Result<(), Error>;
    async fn publish_notification_event(
        self: Arc<Self>,
        event: NotificationEvent,
    ) -> Result<(), Error>;
}

#[derive(Debug)]
pub struct PublisherServiceImpl {
    shutdown_tx: UnboundedSender<()>,
    shutdown_rx: Mutex<UnboundedReceiver<()>>,
}

#[async_trait]
impl PublisherService for PublisherServiceImpl {
    async fn stop(self: Arc<Self>) -> Result<()> {
        info!("shutting down publisher service...");
        _ = self.shutdown_tx.send(());
        Ok(())
    }

    async fn run(self: Arc<Self>) -> Result<(), Error> {
        let _self = self.clone();
        let _self2: Arc<PublisherServiceImpl> = self.clone();
        let mut shutdown_rx = _self2.shutdown_rx.lock().await;
        tokio::select! {
            _ =  shutdown_rx.recv() => {
                info!("publisher service stopped");
                Ok(())
            }
        }
    }

    async fn publish_notification_event(
        self: Arc<Self>,
        event: NotificationEvent,
    ) -> Result<(), Error> {
        let mut channel = Connection::connect(&APP_CONFIG.rmq_uri, ConnectionProperties::default())
            .await?
            .create_channel()
            .await?;
        let channel_state = channel.status().state();
        if channel_state == lapin::ChannelState::Closed
            || channel_state == lapin::ChannelState::Error
        {
            info!("reconnecting to RabbitMQ");
            loop {
                if let Ok(conn) =
                    Connection::connect(&APP_CONFIG.rmq_uri, ConnectionProperties::default()).await
                {
                    if let Ok(c) = conn.create_channel().await {
                        channel = c;
                        break;
                    }
                }
                tokio::time::sleep(std::time::Duration::from_secs(1)).await;
            }
        }
        let channel = channel.clone();
        tokio::spawn(async move {
            let payload = serde_json::to_string(&event).unwrap();
            if let Err(e) = channel
                .basic_publish(
                    NOTIFICATION_EXCHANGE,
                    match event {
                        NotificationEvent::Register(_) => NOTIFICATION_REGISTER_ROUTING_KEY,
                    },
                    BasicPublishOptions::default(),
                    payload.as_bytes(),
                    BasicProperties::default(),
                )
                .await
            {
                warn!(
                    "failed to publish message: {} to exchange: {}, err = {}",
                    payload, NOTIFICATION_EXCHANGE, e
                );
                return;
            }
            info!(
                "successfully published message: {} to exchange: {}",
                payload, NOTIFICATION_EXCHANGE
            )
        });
        Ok(())
    }

    async fn publish_dpn_event(self: Arc<Self>, event: DPNEvent) -> Result<(), Error> {
        let mut channel = Connection::connect(&APP_CONFIG.rmq_uri, ConnectionProperties::default())
            .await?
            .create_channel()
            .await?;
        let channel_state = channel.status().state();
        if channel_state == lapin::ChannelState::Closed
            || channel_state == lapin::ChannelState::Error
        {
            info!("reconnecting to RabbitMQ");
            loop {
                if let Ok(conn) =
                    Connection::connect(&APP_CONFIG.rmq_uri, ConnectionProperties::default()).await
                {
                    if let Ok(c) = conn.create_channel().await {
                        channel = c;
                        break;
                    }
                }
            }
        }
        let channel = channel.clone();
        tokio::spawn(async move {
            let payload = serde_json::to_string(&event).unwrap();
            if let Err(e) = channel
                .basic_publish(
                    EVENTS_EXCHANGE,
                    match event {
                        DPNEvent::PeerConnected(_) | DPNEvent::PeerDisconnected(_) => {
                            CONNECTION_ROUTING_KEY
                        }
                        DPNEvent::SessionCreated(_) | DPNEvent::SessionTerminated(_) => {
                            SESSION_ROUTING_KEY
                        }
                        DPNEvent::Deposit(_) => DEPOSIT_ROUTING_KEY,
                        DPNEvent::Withdrawal(_) => WITHDRAWAL_ROUTING_KEY,
                        DPNEvent::Referral(_) => REFERRAL_ROUTING_KEY,
                    },
                    BasicPublishOptions::default(),
                    payload.as_bytes(),
                    BasicProperties::default(),
                )
                .await
            {
                warn!(
                    "failed to publish message: {} to exchange: {}, err = {}",
                    payload, EVENTS_EXCHANGE, e
                );
                return;
            }
            info!(
                "successfully published message: {} to exchange: {}",
                payload, EVENTS_EXCHANGE
            )
        });
        Ok(())
    }
}

impl PublisherServiceImpl {
    pub fn new() -> Self {
        let (shutdown_tx, shutdown_rx) = tokio::sync::mpsc::unbounded_channel::<()>();

        Self {
            shutdown_tx,
            shutdown_rx: Mutex::new(shutdown_rx),
        }
    }
}
