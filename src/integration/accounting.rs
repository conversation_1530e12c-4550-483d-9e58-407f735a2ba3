use anyhow::Error;
use anyhow::Result;
use dpn_core::types::api::<PERSON>rrorWrapper;
use dpn_core::types::referral::ReferralsOverviewV2;
use dpn_core::types::reward::RewardsOverviewV2;
use reqwest::StatusCode;
use crate::webapi::connections::SessionDetails;
use crate::webapi::users::WithdrawalHistoryDto;
use crate::APP_CONFIG;
use async_trait::async_trait;
use dpn_core::types::{referral::ReferralsOverview, connection::ConnectionOverviewV2, reward::RewardsOverview};
use mockall::automock;
use static_init::dynamic;
use std::{fmt::Debug, sync::Arc};

#[dynamic]
pub static HTTP_CLIENT: reqwest::Client = {
    let mut headers = reqwest::header::HeaderMap::new();
    headers.insert(
        "Content-Type",
        reqwest::header::HeaderValue::from_static("application/json"),
    );
    reqwest::Client::builder()
        .default_headers(headers)
        .build()
        .unwrap()
};

#[automock]
#[async_trait]
pub trait AccountingService: Debug + Send + Sync + 'static {
    async fn get_connection_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<ConnectionOverviewV2, Error>;

    async fn get_connection_history(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<SessionDetails>, Error>;

    async fn get_session_by_hash(
        self: Arc<Self>,
        session_hash: &str,
    ) -> Result<Option<SessionDetails>, Error>;

    async fn get_usage_history(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<SessionDetails>, Error>;

    async fn get_reward_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<RewardsOverviewV2, Error>;

    async fn get_referral_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<ReferralsOverviewV2, Error>;

    async fn get_withdrawal_history(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<WithdrawalHistoryDto>, Error>;
}

#[derive(Debug)]
pub struct AccountingServiceImpl {
    pub connection_overview: String,
    pub connection_history: String,
    pub usage_history: String,
    pub reward_overview: String,
    pub referral_overview: String,
    pub withdrawal_history: String,
    pub session_by_hash: String,
}

impl AccountingServiceImpl {
    pub fn new() -> Self {
        Self {
            connection_overview: "api/connection_overview".to_string(),
            connection_history: "api/connection_history".to_string(),
            usage_history: "api/usage_history".to_string(),
            reward_overview: "api/rewards_overview".to_string(),
            referral_overview: "api/referrals_overview".to_string(),
            withdrawal_history: "api/withdrawal_history".to_string(),
            session_by_hash: "api/session_details".to_string(),
        }
    }

    async fn make_request<T: serde::de::DeserializeOwned>(
        &self,
        endpoint: &str,
        user_addr: &str,
    ) -> Result<T, Error> {
        let path = format!(
            "{}/{}/{}",
            APP_CONFIG.accounting_service_url, endpoint, user_addr,
        );

        let resp = HTTP_CLIENT.get(path).send().await.map_err(|e| {
            Error::msg(format!("request to accounting service failed err = {}", e))
        })?;

        if resp.status() != StatusCode::OK {
            return Err(Error::msg(format!(
                "request to accounting service failed: unknown status code={}",
                resp.status()
            )));
        }

        resp.json::<T>().await.map_err(|e| {
            Error::msg(format!("error parsing response err = {}", e))
        })
    }

    async fn make_request_with_error_wrapper<T: serde::de::DeserializeOwned>(
        &self,
        endpoint: &str,
        user_addr: &str,
    ) -> Result<T, Error> {
        let path = format!(
            "{}/{}/{}",
            APP_CONFIG.accounting_service_url, endpoint, user_addr,
        );

        let resp = HTTP_CLIENT.get(path).send().await.map_err(|e| {
            Error::msg(format!("request to accounting service failed err = {}", e))
        })?;

        match resp.status() {
            StatusCode::OK => {}
            StatusCode::BAD_REQUEST => {
                return Err(Error::msg(format!(
                    "request to accounting service failed: 400 err={}",
                    match resp.json::<ErrorWrapper>().await {
                        Ok(err_wrapper) => err_wrapper.err_msg(),
                        Err(_) => "unknown".to_string(),
                    }
                )));
            }
            _ => {
                return Err(Error::msg(format!(
                    "request to accounting service failed: unknown status code={}",
                    resp.status()
                )));
            }
        }

        resp.json::<T>().await.map_err(|e| {
            Error::msg(format!("error parsing response err = {}", e))
        })
    }
}

#[async_trait]
impl AccountingService for AccountingServiceImpl {
    async fn get_connection_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<ConnectionOverviewV2, Error> {
        self.make_request(&self.connection_overview, &user_addr).await
    }

    async fn get_session_by_hash(
        self: Arc<Self>,
        session_hash: &str,
    ) -> Result<Option<SessionDetails>, Error> {
        self.make_request(&self.session_by_hash, &session_hash).await
    }

    async fn get_connection_history(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<SessionDetails>, Error> {
        self.make_request(&self.connection_history, &user_addr).await
    }

    async fn get_usage_history(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<SessionDetails>, Error> {
        self.make_request(&self.usage_history, &user_addr).await
    }

    async fn get_reward_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<RewardsOverviewV2, Error> {
        self.make_request(&self.reward_overview, &user_addr).await
    }

    async fn get_referral_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<ReferralsOverviewV2, Error> {
        self.make_request_with_error_wrapper(&self.referral_overview, &user_addr).await
    }

    async fn get_withdrawal_history(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<WithdrawalHistoryDto>, Error> {
        self.make_request(&self.withdrawal_history, &user_addr).await
    }
}