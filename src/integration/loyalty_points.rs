use anyhow::Error;
use anyhow::Result;

use crate::APP_CONFIG;
use async_trait::async_trait;
use mockall::automock;
use static_init::dynamic;
use std::time::Duration;
use std::{fmt::Debug, sync::Arc};
use serde_json::json;
use log::error;
use crate::webapi::users::TotalTimeDto;
#[dynamic]
pub static HTTP_CLIENT: reqwest::Client = {
    let mut headers = reqwest::header::HeaderMap::new();
    headers.insert(
        "Content-Type",
        reqwest::header::HeaderValue::from_static("application/json"),
    );
    headers.insert(
        "Connection",
        reqwest::header::HeaderValue::from_static("keep-alive"),
    );
    reqwest::Client::builder()
        .default_headers(headers)
        .connect_timeout(Duration::from_secs(10))
        .build()
        .unwrap()
};

#[automock]
#[async_trait]
pub trait LoyaltyPointsService: Debug + Send + Sync + 'static {
    async fn start_session(
        self: Arc<Self>,
        user_addr: String,
        device_id: String,
    ) -> Result<(), Error>;

    async fn end_session(
        self: Arc<Self>,
        user_addr: String,
        device_id: String,
    ) -> Result<(), Error>;

    async fn heartbeat(
        self: Arc<Self>,
        user_addr: String,
        device_id: String,
    ) -> Result<(), Error>;

    async fn get_total_time(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<TotalTimeDto, Error>;
}   
#[derive(Debug)]
pub struct LoyaltyPointsServiceImpl {
    pub start_session: String,
    pub end_session: String,
    pub heartbeat: String,
    pub get_total_time : String,
}

impl LoyaltyPointsServiceImpl {
    pub fn new() -> Self {
        Self {
            start_session: "api/start_session".to_string(),
            end_session: "api/end_session".to_string(),
            heartbeat: "api/heartbeat".to_string(),
            get_total_time: "api/get_user_online_total_time".to_string(),
        }
    }
}

#[async_trait]
impl LoyaltyPointsService for LoyaltyPointsServiceImpl {
    async fn start_session(
        self: Arc<Self>,
        user_addr: String,
        device_id: String,
    ) -> Result<(), Error> {
        let path = format!(
            "{}/{}",
            APP_CONFIG.loyalty_points_service_url, self.start_session,
        );
        let body = json!({
            "user_addr": user_addr,
            "device_id": device_id,
        });
        
        let response = match HTTP_CLIENT
            .post(path.clone())
            .body(body.to_string())
            .send()
            .await {
                Ok(resp) => resp,
                Err(e) => {
                    error!("Failed to send request to loyalty points service: {}", e);
                    return Err(Error::msg(format!("Failed to send request: {}", e)));
                }
            };
            
        if !response.status().is_success() {
            let status = response.status();
            let error_body = response.text().await.unwrap_or_default();
            error!(
                "Loyalty points service returned error status: {}, body: {}",
                status, error_body
            );
            return Err(Error::msg(format!("Service returned error status: {}", status)));
        }
        
        match response.json::<()>().await {
            Ok(overview) => Ok(overview),
            Err(e) => {
                error!("Failed to parse response from loyalty points service: {}", e);
                Err(Error::msg(format!("Failed to parse response: {}", e)))
            }
        }
    }

    async fn end_session(
        self: Arc<Self>,
        user_addr: String,
        device_id: String,
    ) -> Result<(), Error> {
        let path = format!(
            "{}/{}",
            APP_CONFIG.loyalty_points_service_url, self.end_session
        );
        let body = json!({
            "user_addr": user_addr,
            "device_id": device_id,
        });
        let response = match HTTP_CLIENT
        .post(path.clone())
        .body(body.to_string())
        .send()
        .await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send request to loyalty points service: {}", e);
                return Err(Error::msg(format!("Failed to send request: {}", e)));
            }
        };
        
        if !response.status().is_success() {
            let status = response.status();
            let error_body = response.text().await.unwrap_or_default();
            error!(
                "Loyalty points service returned error status: {}, body: {}",
                status, error_body
            );
            return Err(Error::msg(format!("Service returned error status: {}", status)));
        }
    
        match response.json::<()>().await {
            Ok(overview) => Ok(overview),
            Err(e) => {
                error!("Failed to parse response from loyalty points service: {}", e);
                Err(Error::msg(format!("Failed to parse response: {}", e)))
            }
        }
    }

    async fn heartbeat(
        self: Arc<Self>,
        user_addr: String,
        device_id: String,
    ) -> Result<(), Error> {
        let path = format!(
            "{}/{}",
            APP_CONFIG.loyalty_points_service_url, self.heartbeat
        );
        let body = json!({
            "user_addr": user_addr,
            "device_id": device_id,
        });
        let response = match HTTP_CLIENT
        .post(path.clone())
        .body(body.to_string())
        .send()
        .await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send request to loyalty points service: {}", e);
                return Err(Error::msg(format!("Failed to send request: {}", e)));
            }
        };
        
        if !response.status().is_success() {
            let status = response.status();
            let error_body = response.text().await.unwrap_or_default();
            error!(
                "Loyalty points service returned error status: {}, body: {}",
                status, error_body
            );
            return Err(Error::msg(format!("Service returned error status: {}", status)));
        }

        return Ok(());
        
        // match response.json::<()>().await {
        //     Ok(overview) => Ok(overview),
        //     Err(e) => {
        //         error!("Failed to parse response from loyalty points service: {}", e);
        //         Err(Error::msg(format!("Failed to parse response: {}", e)))
        //     }
        // }
    }

    async fn get_total_time(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<TotalTimeDto, Error> {
        let path = format!(
            "{}/{}/{}",
            APP_CONFIG.loyalty_points_service_url, self.get_total_time, user_addr
        );
        let response = match HTTP_CLIENT
        .get(path.clone())
        .send()
        .await {
            Ok(resp) => resp,
            Err(e) => {
                error!("Failed to send request to loyalty points service: {}", e);
                return Err(Error::msg(format!("Failed to send request: {}", e)));
            }
        };
        
        if !response.status().is_success() {
            let status = response.status();
            let error_body = response.text().await.unwrap_or_default();
            error!(
                "Loyalty points service returned error status: {}, body: {}",
                status, error_body
            );
            return Err(Error::msg(format!("Service returned error status: {}", status)));
        }
        
        match response.json::<TotalTimeDto>().await {
            Ok(overview) => Ok(overview),
            Err(e) => {
                error!("Failed to parse response from loyalty points service: {}", e);
                Err(Error::msg(format!("Failed to parse response: {}", e)))
            }
        }
    }
}
