use crate::{
    webapi::clients::AlgoProxyInfo,
    APP_CONFIG,
};
use anyhow::Error;
use log::info;
use async_trait::async_trait;
use mockall::automock;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use static_init::dynamic;
use utoipa::ToSchema;
use std::fmt::Debug;
use std::sync::Arc;
use std::time::Instant;

#[derive(Debug, <PERSON>lone, Deserialize, Serialize, ToSchema)]
pub struct MasternodeAlgoProxyReq {
    pub client_addr: String,
    pub country_geoname_id: i64,
    pub algo_id_session: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MasternodeProxyHealthCheckResp {
    pub proxy_acc_exists: bool,
    pub proxy_acc_id: Option<String>,
}

#[dynamic]
pub static HTTP_CLIENT: reqwest::Client = {
    let mut headers = reqwest::header::HeaderMap::new();
    headers.insert(
        "Content-Type",
        reqwest::header::HeaderValue::from_static("application/json"),
    );
    reqwest::Client::builder()
        .default_headers(headers)
        .pool_idle_timeout(std::time::Duration::from_secs(30))
        .pool_max_idle_per_host(400)
        .tcp_keepalive(std::time::Duration::from_secs(30))
        .connect_timeout(std::time::Duration::from_secs(10))
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .unwrap()
};

#[automock]
#[async_trait]
pub trait MasternodeClientService: Debug + Send + Sync + 'static {
    async fn create_algo_proxy(
        self: Arc<Self>,
        ms_algo_proxy_req: MasternodeAlgoProxyReq,
        masternode_ip: String,
    ) -> Result<AlgoProxyInfo, Error>;
    async fn delete_algo_proxy(
        self: Arc<Self>,
        algo_id_session: &str,
        masternode_ip: &str,
    ) -> Result<String, Error>;
    async fn health_check_proxy(
        self: Arc<Self>,
        algo_id_session: &str,
        masternode_ip: &str,
    ) -> Result<MasternodeProxyHealthCheckResp, Error>;
}

#[derive(Debug)]
pub struct MasternodeClientServiceImpl {
    pub algo_proxy: String,
    pub health_check_proxy: String,
}

impl MasternodeClientServiceImpl {
    pub fn new() -> Self {
        Self {
            algo_proxy: "masternode/algo/proxies".to_string(),
            health_check_proxy: "masternode/proxy_health".to_string(),
        }
    }
}

#[async_trait]
impl MasternodeClientService for MasternodeClientServiceImpl {
    async fn create_algo_proxy(
        self: Arc<Self>,
        ms_algo_proxy_req: MasternodeAlgoProxyReq,
        masternode_ip: String,
    ) -> Result<AlgoProxyInfo, Error> {
        let setup_time = Instant::now();
        let mut path: String = String::new();
        if masternode_ip == "" {
            path = format!("{}/{}", APP_CONFIG.masternode_client_url, self.algo_proxy,);
        }else{
            let host_masternode_ip = format!("http://{}:9093", masternode_ip);
            path = format!("{}/{}", host_masternode_ip, self.algo_proxy,);
        }
        info!("setup request masternode with path: {} with duration: {:?}", path, setup_time.elapsed());

        let http_request_time = Instant::now();
        match HTTP_CLIENT
            .post(path)
            .json(&ms_algo_proxy_req)
            .header("x-api-key", APP_CONFIG.x_api_key.clone())
            .send()
            .await
        {
            Ok(resp) => {
                if resp.status() != StatusCode::OK {
                    info!("Get response code !200 duration: {:?}", http_request_time.elapsed());
                    return Err(Error::msg(format!(
                        "request to master node client failed: unknown status code={}",
                        resp.status()
                    )));
                } else {
                    info!("Get response code 200 duration: {:?}", http_request_time.elapsed());
                    match resp.json::<AlgoProxyInfo>().await {
                        Ok(algo_proxy_info) => Ok(algo_proxy_info),
                        Err(e) => {
                            return Err(Error::msg(format!(
                                "error parsing algo proxy info response err = {}",
                                e
                            )));
                        }
                    }
                }
            }
            Err(e) => {
                return Err(Error::msg(format!(
                    "request to master node client failed err = {}",
                    e
                )));
            }
        }
    }

    async fn delete_algo_proxy(
        self: Arc<Self>,
        algo_id_session: &str,
        masternode_ip: &str,
    ) -> Result<String, Error> {
        let mut path: String = String::new();
        if masternode_ip == "" {
            path = format!("{}/{}/{}", APP_CONFIG.masternode_client_url, self.algo_proxy, algo_id_session);
        }else{
            let host_masternode_ip = format!("http://{}:9093", masternode_ip);
            path = format!("{}/{}/{}", host_masternode_ip, self.algo_proxy, algo_id_session);
        }
        match HTTP_CLIENT
            .delete(path)
            .header("x-api-key", APP_CONFIG.x_api_key.clone())
            .send()
            .await
        {
            Ok(resp) => {
                if resp.status() != StatusCode::OK {
                    return Err(Error::msg(format!(
                        "request to master node client failed: unknown status code={}",
                        resp.status()
                    )));
                } else {
                    Ok(algo_id_session.to_string())
                }
            }
            Err(e) => {
                return Err(Error::msg(format!(
                    "request to master node client failed err = {}",
                    e
                )));
            }
        }
    }

    async fn health_check_proxy(
        self: Arc<Self>,
        algo_id_session: &str,
        masternode_ip: &str,
    ) -> Result<MasternodeProxyHealthCheckResp, Error> {
        let setup_time = Instant::now();
        let path = if masternode_ip.is_empty() {
            format!("{}/{}/{}", APP_CONFIG.masternode_client_url, self.health_check_proxy, algo_id_session)
        } else {
            format!("http://{}:9093/{}/{}", masternode_ip, self.health_check_proxy, algo_id_session)
        };
        info!("finished setup duration: {:?}", setup_time.elapsed());

        let http_request_time = Instant::now();
 
        // Add timeout to the request
        let request = HTTP_CLIENT
            .get(&path)
            .header("x-api-key", APP_CONFIG.x_api_key.clone())
            .timeout(std::time::Duration::from_secs(2)); // 2 second timeout

        match request.send().await {
            Ok(resp) => {
                if resp.status() != StatusCode::OK {
                    info!("Finished with status !200: {:?}", http_request_time.elapsed());
                    return Err(Error::msg(format!(
                        "request to master node client failed: unknown status code={}",
                        resp.status()
                    )));
                }
                
                info!("Finished with status 200: {:?}", http_request_time.elapsed());
                match resp.json::<MasternodeProxyHealthCheckResp>().await {
                    Ok(resp) => Ok(resp),
                    Err(e) => Err(Error::msg(format!(
                        "error parsing algo proxy info response err = {}",
                        e
                    )))
                }
            }
            Err(e) => {
                info!("Finished with error: {:?}", http_request_time.elapsed());
                Err(Error::msg(format!(
                    "request to master node client failed err = {}",
                    e
                )))
            }
        }
    }
}