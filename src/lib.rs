#![feature(more_qualified_paths)]
#![feature(iter_collect_into)]
#![feature(addr_parse_ascii)]
use serde::{Deserialize, Serialize};
use static_init::dynamic;

pub mod bootnode;
pub mod connection;
pub mod events_queue;
pub mod integration;
pub mod monitoring;
pub mod user;
pub mod location;
pub mod webapi;
pub mod utils;

#[dynamic]
pub static APP_CONFIG: AppConfig = {
    let mut file = std::fs::File::open("config.yaml").unwrap();
    let mut contents = String::new();
    std::io::Read::read_to_string(&mut file, &mut contents).unwrap();
    serde_yaml::from_str(&contents).unwrap()
};

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct AppConfig {
    pub log_level: String,
    pub web_bind: String,
    pub db_url: String,
    pub jwt_secret_key: String,
    pub x_api_key: String,
    pub mmdb_path: String,
    pub rmq_uri: String,
    pub accounting_service_url: String,
    pub loyalty_points_service_url: String,
    pub redis_uri: String,
    pub country_info_file_path: String,
    pub masternode_client_url: String,
    
    // masternode config
    pub masternode_country_code_file_path: String,
    pub masternode0: String,
    pub masternode1: String,
    pub masternode2: String,

    // wallet cli address
    pub wallet_cli_address: String,

    // online provider stats url
    pub online_provider_stats_url: String,
}
