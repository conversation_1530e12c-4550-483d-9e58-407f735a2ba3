use self::store::LocationStorage;
use anyhow::{anyhow, Error, Result};
use async_trait::async_trait;
use dpn_core::services::redis::{DPNRedisKey, RedisService};
use dpn_core::types::bonus_config::{BonusConfig, BonusInfo};
use dpn_core::types::geo::{Continent, Country};
use dpn_db::model::storage_location::LocationCountry;
use log::{error, info};
use mockall::automock;
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};
use tokio::sync::Mutex;
use std::{fmt::Debug, sync::Arc};

pub mod store;

#[automock]
#[async_trait]
pub trait LocationService: Debug + Send + Sync + 'static {
    async fn add_bonus_info_to_redis(self: Arc<Self>) -> Result<usize, Error>;
    async fn delete_bonus_info_from_redis(self: Arc<Self>) -> anyhow::Result<()>;
    async fn get_continents(self: Arc<Self>) -> anyhow::Result<Vec<Continent>>;
    async fn get_countries(self: Arc<Self>) -> anyhow::Result<Vec<LocationCountry>>;
    async fn get_countries_by_continent(
        self: Arc<Self>,
        continent_geoname_id: i32,
    ) -> anyhow::Result<Vec<Country>>;
    async fn get_bonus_info_by_country(
        self: Arc<Self>,
        country_geoname_id: i32,
    ) -> anyhow::Result<Option<BonusConfig>>;
    async fn get_all_bonus_info(self: Arc<Self>) -> anyhow::Result<Vec<BonusInfo>>;
}

#[derive(Debug)]
pub struct LocationServiceImpl {
    store: Arc<LocationStorage>,
    redis_svc: Arc<RedisService>,
    shutdown_tx: UnboundedSender<()>,
    shutdown_rx: Mutex<UnboundedReceiver<()>>,
}

#[async_trait]
impl LocationService for LocationServiceImpl {
    async fn add_bonus_info_to_redis(self: Arc<Self>) -> Result<usize, Error> {
        let bonus_info = self
            .store
            .clone()
            .get_all_bonus_info()
            .await
            .map_err(|e| anyhow!("{}", e))?;
        for bonus in bonus_info.iter() {
            let (k, f) = (
                DPNRedisKey::get_bonus_config_hash_key(),
                bonus.country_geoname_id.to_string(),
            );
            self.redis_svc
                .clone()
                .hset(k, f, bonus.bonus_amount)
                .map_err(|e| anyhow!("{}", e))?;
        }
        Ok(bonus_info.len())
    }

    async fn delete_bonus_info_from_redis(self: Arc<Self>) -> anyhow::Result<()> {
        self.redis_svc
            .clone()
            .del(DPNRedisKey::get_bonus_config_hash_key())
            .map_err(|e| anyhow!("{}", e))?;
        Ok(())
    }

    async fn get_bonus_info_by_country(
        self: Arc<Self>,
        country_geoname_id: i32,
    ) -> anyhow::Result<Option<BonusConfig>> {
        let _store = self.store.clone();
        match _store
            .clone()
            .get_bonus_info_by_country(country_geoname_id)
            .await
        {
            Ok(bonus_info) => return Ok(bonus_info),
            Err(e) => return Err(anyhow::anyhow!("{}", e)),
        };
    }

    async fn get_all_bonus_info(self: Arc<Self>) -> anyhow::Result<Vec<BonusInfo>> {
        let _store = self.store.clone();
        match _store.clone().get_all_bonus_info().await {
            Ok(bonus_info) => return Ok(bonus_info),
            Err(e) => return Err(anyhow::anyhow!("{}", e)),
        };
    }



    async fn get_continents(self: Arc<Self>) -> anyhow::Result<Vec<Continent>> {
        let _store = self.store.clone();
        match _store.clone().get_continents().await {
            Ok(continents) => return Ok(continents),
            Err(e) => return Err(anyhow::anyhow!("{}", e)),
        };
    }

    async fn get_countries(self: Arc<Self>) -> anyhow::Result<Vec<LocationCountry>> {
        let _store = self.store.clone();
        match _store.clone().get_countries().await {
            Ok(countries) => return Ok(countries),
            Err(e) => return Err(anyhow::anyhow!("{}", e)),
        };
    }

    async fn get_countries_by_continent(
        self: Arc<Self>,
        continent_geoname_id: i32,
    ) -> anyhow::Result<Vec<Country>> {
        let _store = self.store.clone();
        match _store
            .clone()
            .get_countries_by_continent(continent_geoname_id)
            .await
        {
            Ok(countries) => return Ok(countries),
            Err(e) => return Err(anyhow::anyhow!("{}", e)),
        };
    }
}

impl LocationServiceImpl {
    pub async fn new(
        location_store: Arc<LocationStorage>,
        redis_svc: Arc<RedisService>,
    ) -> Result<Self, Error> {
        let (shutdown_tx, shutdown_rx) = tokio::sync::mpsc::unbounded_channel::<()>();

        Ok(Self {
            store: location_store,
            redis_svc,
            shutdown_tx,
            shutdown_rx: Mutex::new(shutdown_rx),
        })
    }

    pub async fn run(self: Arc<Self>) -> Result<(), Error> {
        let _self = self.clone();
        let _self2 = self.clone();
        let mut shutdown_rx = _self2.shutdown_rx.lock().await;

        if let Err(e) = self.clone().delete_bonus_info_from_redis().await {
            return Err(anyhow!(
                "failed to delete bonus info from redis err={}",
                e
            ));
        }

        match self.clone().add_bonus_info_to_redis().await {
            Ok(total) => {
                info!("redis: uploaded bonus info total={}", total);
            }
            Err(e) => {
                error!("redis: upload bonus info failed err={}", e);
                return Err(anyhow!("redis: upload bonus info failed err={}", e));
            }
        }

        tokio::select! {
            _ =  shutdown_rx.recv() => {
                info!("service stopped");
                Ok(())
            },
        }
    }

    pub async fn stop(self: Arc<Self>) -> Result<()> {
        info!("shutting down service...");
        let _self = self.clone();
        // let _self2 = self.clone();
        _ = _self.shutdown_tx.send(());
        Ok(())
    }
}
