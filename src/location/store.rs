use anyhow::Error;
use dpn_core::types::{
    bonus_config::{BonusConfig, BonusInfo},
    geo::{Continent, Country},
};
use dpn_db::{connection::ConnectionPool, model::storage_location::LocationCountry};
use std::sync::Arc;

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct LocationStorage {
    db_conn: ConnectionPool,
}

impl LocationStorage {
    pub async fn new(db_url: &str) -> Result<Self, Error> {
        let db_conn = ConnectionPool::builder(db_url, 10).build().await;
        if let Err(e) = db_conn {
            return Err(Error::msg(format!(
                "cannot construct db conn for location storage: error={}",
                e
            )));
        }

        Ok(Self {
            db_conn: db_conn.unwrap(),
        })
    }

    pub async fn get_bonus_info_by_country(
        self: Arc<Self>,
        country_geoname_id: i32,
    ) -> anyhow::Result<Option<BonusConfig>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut bonus_config_dal = ast.bonus_config_dal();
        match bonus_config_dal
            .get_bonus_info_by_country(country_geoname_id)
            .await
        {
            Ok(bonus_info) => Ok(bonus_info),
            Err(e) => {
                if e.to_string().contains("no rows in result set") {
                    return Ok(None);
                }
                Err(e)
            }
        }
    }

    pub async fn get_all_bonus_info(self: Arc<Self>) -> anyhow::Result<Vec<BonusInfo>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut bonus_config_dal = ast.bonus_config_dal();
        match bonus_config_dal.get_all_bonus_info().await {
            Ok(bonus_info) => Ok(bonus_info),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn get_continents(self: Arc<Self>) -> anyhow::Result<Vec<Continent>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut location_dal = ast.location_dal();
        match location_dal.get_continents().await {
            Ok(continents) => Ok(continents),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn get_countries(self: Arc<Self>) -> anyhow::Result<Vec<LocationCountry>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut location_dal = ast.location_dal();
        match location_dal.get_countries().await {
            Ok(countries) => Ok(countries),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }

    pub async fn get_countries_by_continent(
        self: Arc<Self>,
        continent_geoname_id: i32,
    ) -> anyhow::Result<Vec<Country>> {
        let _self = self.clone();
        let mut ast = _self.db_conn.access_storage().await?;
        let mut location_dal = ast.location_dal();
        match location_dal
            .get_countries_by_continent(continent_geoname_id)
            .await
        {
            Ok(countries) => Ok(countries),
            Err(e) => Err(anyhow::anyhow!("{}", e)),
        }
    }
}
