use anyhow::{Error, Result};
use dpn_admin::{
    bootnode::BootnodeServiceImpl,
    connection::{store::ConnectionStorage, ConnectionServiceImpl},
    events_queue::{
        consumer::ConsumerServiceImpl,
        publisher::{PublisherService, PublisherServiceImpl},
        setup_rabbitmq,
    },
    integration::{
        accounting::AccountingServiceImpl, loyalty_points::LoyaltyPointsServiceImpl,
        masternode_client::MasternodeClientServiceImpl,
    },
    location::{store::LocationStorage, LocationServiceImpl},
    user::{store::UserStorage, UserServiceImpl},
    utils::iso_to_geoname_id::IsoToGeonameIdService,
    webapi::run_web,
    APP_CONFIG,
};
use dpn_core::services::redis::RedisService;
use dpn_redis_2::RedisService as RedisService2;
use log::{debug, info};
use std::sync::Arc;
use tokio::signal::unix::{signal, SignalKind};

#[actix_web::main]
async fn main() -> Result<()> {
    env_logger::init_from_env(env_logger::Env::new().default_filter_or(&APP_CONFIG.log_level));
    rustls::crypto::ring::default_provider()
        .install_default()
        .expect("Failed to install rustls crypto provider");

    if let Err(e) = setup_rabbitmq().await {
        info!("Failed to set up RabbitMQ: {}", e);
        return Ok(());
    }
    debug!("redis uri: {}", APP_CONFIG.redis_uri);
    let redis_svc = Arc::new(RedisService::new(APP_CONFIG.redis_uri.clone()).await?);
    let redis_svc_2 = Arc::new(RedisService2::new(&APP_CONFIG.redis_uri).await?);
    let publisher_svc: Arc<dyn PublisherService> = Arc::new(PublisherServiceImpl::new());

    let user_store = Arc::new(UserStorage::new(&APP_CONFIG.db_url).await?);
    let user_svc = Arc::new(
        UserServiceImpl::new(redis_svc.clone(), user_store.clone(), publisher_svc.clone()).await?,
    );

    let iso_to_geoname_id_svc = Arc::new(IsoToGeonameIdService::new(
        APP_CONFIG.country_info_file_path.clone(),
    )?);
    let ms_client_svc = Arc::new(MasternodeClientServiceImpl::new());

    let connection_store = Arc::new(ConnectionStorage::new(&APP_CONFIG.db_url).await?);
    let connection_svc = Arc::new(
        ConnectionServiceImpl::new(
            connection_store.clone(),
            user_svc.clone(),
            redis_svc.clone(),
            iso_to_geoname_id_svc.clone(),
            ms_client_svc.clone(),
        )
        .await?,
    );

    let consumer_svc = Arc::new(ConsumerServiceImpl::new(connection_svc.clone()).await?);
    let bootnode_svc = Arc::new(BootnodeServiceImpl::new(connection_svc.clone(), redis_svc.clone(), redis_svc_2.clone()));
    // boot masternode country code map
    bootnode_svc
        .clone()
        .load_masternode_country_code_map()
        .await?;
    debug!(
        "masternode country code map: {:?}",
        bootnode_svc.masternode_country_code_map
    );

    let accounting_svc = Arc::new(AccountingServiceImpl::new());
    let connection_svc = Arc::new(
        ConnectionServiceImpl::new(
            connection_store.clone(),
            user_svc.clone(),
            redis_svc.clone(),
            iso_to_geoname_id_svc.clone(),
            ms_client_svc.clone(),
        )
        .await?,
    );

    let location_store = Arc::new(LocationStorage::new(&APP_CONFIG.db_url).await?);
    let location_svc =
        Arc::new(LocationServiceImpl::new(location_store.clone(), redis_svc.clone()).await?);
    let loyalty_points_svc = Arc::new(LoyaltyPointsServiceImpl::new());

    _ = tokio::try_join! {
        sig_term(
            connection_svc.clone(),
            location_svc.clone(),
            consumer_svc.clone(),
            publisher_svc.clone(),
            user_svc.clone(),
        ),
        //connection_svc.clone().run(), // debug later
        // location_svc.clone().run(), // debug later
        consumer_svc.clone().run(), 
        user_svc.clone().run(), 
        run_web(
            user_svc.clone(),
            connection_svc.clone(),
            bootnode_svc.clone(),
            accounting_svc.clone(),
            location_svc.clone(),
            loyalty_points_svc.clone(),
        ),
    };
    Ok(())
}

async fn sig_term(
    connection_svc: Arc<ConnectionServiceImpl>,
    location_svc: Arc<LocationServiceImpl>,
    consumer_svc: Arc<ConsumerServiceImpl>,
    publisher_svc: Arc<dyn PublisherService>,
    user_svc: Arc<UserServiceImpl>,
) -> Result<(), Error> {
    let mut sigint = signal(SignalKind::interrupt())?;
    let mut sigterm = signal(SignalKind::terminate())?;

    tokio::select! {
        _ = sigint.recv() => {},
        _ = sigterm.recv() => {}
    };

    info!("SIGINT/SIGTERM received");
    _ = connection_svc.clone().stop().await;
    _ = location_svc.clone().stop().await;
    _ = consumer_svc.clone().stop().await;
    _ = publisher_svc.clone().stop().await;
    _ = user_svc.clone().stop().await;

    Ok(())
}

#[tokio::test]
async fn test_loadtest_heathcheck_proxy() {
    use reqwest::Client;
    use std::time::Instant;
    use tokio::task::JoinSet;
    use log::info;

    let client = Client::new();
    
    let base_url = "https://dpn-masternode-dev.uniultra.xyz/clients/algo/proxies/health/0196b73a1d5c7ddebb66e662bbf04eee";
    let auth_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************.OgXsgGR_FkpzUNFX0TlTL10l78ubCSoP4JOxcoOognk"; // Replace with actual bearer token
    let num_requests = 300;
    let start_time = Instant::now();

    let mut set = JoinSet::new();

    // Spawn 300 concurrent requests
    for i in 0..num_requests {
        let client = client.clone();
        let url = format!("{}", base_url);
        
        set.spawn(async move {
            let response = client
                .get(&url)
                .header("Authorization", format!("Bearer {}", auth_token))
                .send()
                .await;
            
            match response {
                Ok(resp) => {
                    let status = resp.status();
                    let body = resp.text().await.unwrap_or_default();
                    info!("Request {} completed with status: {}, body: {}", i, status, body);
                }
                Err(e) => {
                    info!("Request {} failed: {}", i, e);
                }
            }
        });
    }

    // Wait for all requests to complete
    while let Some(result) = set.join_next().await {
        if let Err(e) = result {
            info!("Task failed: {}", e);
        }
    }

    let duration = start_time.elapsed();
    info!("Load test completed in {:?}", duration);
    info!("Average request time: {:?}", duration / num_requests as u32);
}
