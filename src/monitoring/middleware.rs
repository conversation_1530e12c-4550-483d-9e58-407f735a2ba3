use actix_service::Service;
use actix_web::dev::{ServiceRequest, ServiceResponse};
use actix_web::Error;
use prometheus::HistogramTimer;

use crate::monitoring::metrics::{HTTP_REQUESTS_TOTAL, HTTP_RESPONSE_TIME_SECONDS};

pub async fn monitoring_middleware<S>(
    req: ServiceRequest,
    srv: &S,
) -> Result<ServiceResponse, Error>
where
    S: Service<ServiceRequest, Response = ServiceResponse, Error = Error>,
{
    let mut histogram_timer: Option<HistogramTimer> = None;
    let request_path = req.path();
    let is_registered_resource = req.resource_map().has_resource(request_path);

    // Check if the resource is registered to prevent possible DoS attacks
    if is_registered_resource {
        let request_method = req.method().to_string();

        histogram_timer = Some(
            HTTP_RESPONSE_TIME_SECONDS
                .with_label_values(&[&request_method, request_path])
                .start_timer(),
        );

        HTTP_REQUESTS_TOTAL
            .with_label_values(&[&request_method, request_path])
            .inc();
    }

    let fut = srv.call(req);
    let res = fut.await?;

    if let Some(histogram_timer) = histogram_timer {
        histogram_timer.observe_duration();
    };

    Ok(res)
}
