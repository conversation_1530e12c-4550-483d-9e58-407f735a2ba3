use actix_web::HttpRequest;
use log::{debug, info};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};
use uuid::Uuid;

/// Represents a trace context for API requests
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TraceContext {
    /// Unique identifier for the request
    pub request_id: String,
    /// IP address of the client
    pub client_ip: String,
    /// User agent of the client
    pub user_agent: String,
    /// Timestamp when the request was received
    pub timestamp: u64,
    /// The endpoint path that was called
    pub endpoint: String,
    /// Additional context information
    pub additional_info: Option<String>,
}

impl TraceContext {
    /// Create a new trace context from an HTTP request
    pub fn from_request(req: &HttpRequest, endpoint: &str) -> Self {
        // Generate a unique request ID
        let request_id = Uuid::new_v4().to_string();
        
        // Get client IP address
        let client_ip = req
            .connection_info()
            .realip_remote_addr()
            .unwrap_or("unknown")
            .to_string();
        
        // Get user agent
        let user_agent = req
            .headers()
            .get("user-agent")
            .and_then(|h| h.to_str().ok())
            .unwrap_or("unknown")
            .to_string();
        
        // Get current timestamp
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        TraceContext {
            request_id,
            client_ip,
            user_agent,
            timestamp,
            endpoint: endpoint.to_string(),
            additional_info: None,
        }
    }
    
    /// Log the start of request processing
    pub fn log_request_start(&self) {
        info!(
            "[TRACE] Request started: id={}, endpoint={}, client_ip={}, user_agent={}, timestamp={}",
            self.request_id, self.endpoint, self.client_ip, self.user_agent, self.timestamp
        );
    }
    
    /// Log the end of request processing with status
    pub fn log_request_end(&self, status: &str) {
        info!(
            "[TRACE] Request completed: id={}, endpoint={}, status={}, client_ip={}",
            self.request_id, self.endpoint, status, self.client_ip
        );
    }
    
    /// Add additional context information
    pub fn with_additional_info(mut self, info: &str) -> Self {
        self.additional_info = Some(info.to_string());
        self
    }
    
    /// Log detailed debug information
    pub fn log_debug(&self, message: &str) {
        debug!(
            "[TRACE] Debug: id={}, endpoint={}, message={}, additional_info={:?}",
            self.request_id, self.endpoint, message, self.additional_info
        );
    }
}
