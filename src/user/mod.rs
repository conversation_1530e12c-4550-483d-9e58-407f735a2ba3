pub mod store;

use self::store::UserStorage;
use crate::{
    events_queue::publisher::PublisherService,
    monitoring::profiling::CpuProfiler,
    webapi::{
        clients::{ClientOverview, ProxyAccUpdate},
        connections::BandwidthPrice, users::UserOnlineSession,
    }, APP_CONFIG,
};
use anyhow::Error;
use async_trait::async_trait;
use dpn_core::{
    services::{redis::RedisService, types::ProxyAccChanged},
    types::{
        bandwidth::UserBandwidthPrice,
        msg_queue::{DPNEvent, NotificationEvent, ReferralExtra, WithdrawalExtra},
        noti::NotificationRegister,
        referral::{Referral, ReferralsOverview},
        tier::{Tier, TierPoint, UserTier},
        tx::Tx,
        user::User,
    },
    utils::{bytes_to_hex_string, hash::hash},
};
use dpn_db::{model::storage_user::StorageUser, proxy_acc_dal::ProxyAccDto};
use ethers::types::Address;
use futures_util::lock::Mutex;
use log::{debug, error, info};
use mockall::automock;
use moka::future::Cache;
use std::{fmt::Debug, sync::Arc, time::Duration};
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};

#[automock]
#[async_trait]
pub trait UserService: Debug + Send + Sync + 'static {
    async fn create_user(
        self: Arc<Self>,
        user: User,
        suggested_bandwidth_price: BandwidthPrice,
    ) -> Result<User, Error>;
    async fn create_client_user(self: Arc<Self>, user: User) -> Result<User, Error>;
    async fn reset_password(
        self: Arc<Self>,
        email: String,
        salt: String,
        password_hashed: String,
    ) -> Result<(), Error>;
    async fn get_user_by_addr(self: Arc<Self>, user_addr: String) -> Result<Option<User>, Error>;
    async fn get_user_by_email(self: Arc<Self>, email: String) -> Result<Option<User>, Error>;
    async fn is_username_existed(self: Arc<Self>, username: String) -> Result<bool, Error>;
    async fn is_email_existed(self: Arc<Self>, email: String) -> Result<bool, Error>;
    async fn get_tier_of(self: Arc<Self>, user_addr: String) -> Result<UserTier, Error>;
    async fn get_user_commission_rate(self: Arc<Self>, user_addr: String) -> (i64, i64);
    async fn get_bandwidth_price_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<UserBandwidthPrice, Error>;
    async fn update_bandwidth_price_of(
        self: Arc<Self>,
        user_addr: String,
        rate_per_kb: i64,
        rate_per_second: i64,
    ) -> Result<(), Error>;
    async fn get_referrals_overview_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<ReferralsOverview, Error>;
    async fn get_referral_of(self: Arc<Self>, user_addr: String) -> Result<Referral, Error>;
    async fn get_referrals_by(self: Arc<Self>, user_addr: String) -> Result<Vec<Referral>, Error>;
    async fn create_referral_code_of(
        self: Arc<Self>,
        user_addr: String,
        referral_code: String,
    ) -> Result<String, Error>;
    async fn import_referral_code_of(
        self: Arc<Self>,
        user_addr: String,
        friend_referral_code: String,
    ) -> Result<(), Error>;
    async fn get_total_pending_withdrawal_txs_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<i64, Error>;
    async fn withdraw_reward(
        self: Arc<Self>,
        user_addr: String,
        withdraw_addr: String,
    ) -> Result<(), Error>;
    async fn get_withdrawal_addr_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Option<Address>>;
    async fn get_withdrawals_of(self: Arc<Self>, user_addr: String) -> anyhow::Result<Vec<Tx>>;
    async fn update_withdrawal_addr_of(
        self: Arc<Self>,
        user_addr: String,
        withdrawal_addr: Address,
    ) -> anyhow::Result<()>;
    async fn update_proxy_account(
        self: Arc<Self>,
        user_addr: String,
        proxy_acc_request: ProxyAccUpdate,
    ) -> Result<(), Error>;
    async fn delete_proxy_account(
        self: Arc<Self>,
        user_addr: String,
        proxy_acc_id: String,
    ) -> Result<(), Error>;
    async fn get_client_proxy_accounts(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<ProxyAccDto>, Error>;
    async fn get_user_tier_points(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Vec<TierPoint>>;
    async fn add_user_tier_points(
        self: Arc<Self>,
        user_addr: String,
        points: i64,
    ) -> anyhow::Result<()>;
    async fn get_client_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<ClientOverview, Error>;
    async fn register_notification(
        self: Arc<Self>,
        noti_register: NotificationRegister,
    ) -> Result<(), Error>;
    async fn get_online_sessions_of_user(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<UserOnlineSession>, Error>;

    async fn get_user_by_pincode(self: Arc<Self>, pincode: String) -> Result<Option<User>, Error>;
}

#[derive(Debug)]
pub struct UserServiceImpl {
    store: Arc<UserStorage>,
    redis_svc: Arc<RedisService>,
    publisher_svc: Arc<dyn PublisherService>,
    shutdown_tx: UnboundedSender<()>,
    shutdown_rx: Mutex<UnboundedReceiver<()>>,
    // Cache for users by email
    email_cache: Cache<String, Option<User>>,
}

#[async_trait]
impl UserService for UserServiceImpl {
    async fn create_user(
        self: Arc<Self>,
        user: User,
        bandwidth_price: BandwidthPrice,
    ) -> Result<User, Error> {
        let _store = self.store.clone();
        let _user: StorageUser = user.clone().into();
        match _store
            .clone()
            .create_user(_user.clone(), bandwidth_price)
            .await
        {
            Ok(created_user) => {
                // Add the new user to the cache if it has an email
                if let Some(email) = created_user.email.clone() {
                    self.email_cache.insert(email, Some(created_user.clone())).await;
                }
                Ok(created_user)
            },
            Err(e) => return Err(Error::msg(format!("{}", e))),
        }
    }

    async fn create_client_user(self: Arc<Self>, user: User) -> Result<User, Error> {
        let _store = self.store.clone();
        let _user: StorageUser = user.clone().into();

        match _store.clone().create_client_user(_user.clone()).await {
            Ok(created_user) => {
                // Add the new user to the cache if it has an email
                if let Some(email) = created_user.email.clone() {
                    self.email_cache.insert(email, Some(created_user.clone())).await;
                }
                Ok(created_user)
            },
            Err(e) => return Err(Error::msg(format!("{}", e))),
        }
    }

    async fn reset_password(
        self: Arc<Self>,
        username: String,
        salt: String,
        password_hashed: String,
    ) -> Result<(), Error> {
        let _store = self.store.clone();
        match _store
            .clone()
            .update_user_password(username.clone(), salt, password_hashed)
            .await
        {
            Ok(_) => {
                // Invalidate the cache for this user
                self.email_cache.invalidate(&username).await;
                debug!("Invalidated cache for user email: {}", username);
                return Ok(());
            },
            Err(e) => return Err(Error::msg(format!("{}", e))),
        }
    }

    async fn is_username_existed(self: Arc<Self>, username: String) -> Result<bool, Error> {
        let _store = self.store.clone();
        match _store.clone().is_username_existed(username).await {
            Ok(result) => return Ok(result),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn is_email_existed(self: Arc<Self>, email: String) -> Result<bool, Error> {
        let _store = self.store.clone();
        match _store.clone().is_email_existed(email).await {
            Ok(result) => return Ok(result),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_user_by_addr(self: Arc<Self>, user_addr: String) -> Result<Option<User>, Error> {
        let _profiler = CpuProfiler::new("get_user_by_addr", "UserService");
        let _store = self.store.clone();
        match _store.clone().get_user_by_id(user_addr).await {
            Ok(result) => return Ok(result),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_user_by_email(self: Arc<Self>, email: String) -> Result<Option<User>, Error> {
        // Try to get from cache first
        if let Some(cached_user) = self.email_cache.get(&email).await {
            debug!("Using cached user for email: {}", email);
            return Ok(cached_user);
        }

        // If not in cache, fetch from database
        let _profiler = CpuProfiler::new("get_user_by_email", "UserService");
        let _store = self.store.clone();
        match _store.clone().get_user_by_email(email.clone()).await {
            Ok(result) => {
                // Store in cache for future requests
                self.email_cache.insert(email, result.clone()).await;
                return Ok(result);
            },
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_tier_of(self: Arc<Self>, user_addr: String) -> Result<UserTier, Error> {
        let _store = self.store.clone();
        match _store.clone().get_tier_by_user_id(user_addr).await {
            Ok(result) => return Ok(result),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_user_commission_rate(self: Arc<Self>, user_addr: String) -> (i64, i64) {
        let _store = self.store.clone();
        match _store.clone().get_tier_by_user_id(user_addr).await {
            Ok(storage_user_tier) => {
                let user_tier: UserTier = storage_user_tier.into();

                match user_tier.tier {
                    Tier::None => return (1_500, 500),
                    Tier::Bronze => return (1_500, 500),
                    Tier::Silver => return (1_300, 500),
                    Tier::Gold => return (1_100, 500),
                    Tier::Platinum => return (900, 500),
                    Tier::Diamond => return (700, 500),
                    Tier::None => return (1_500, 500),
                }
            }
            Err(_) => return (1_500, 500),
        };
    }

    async fn get_bandwidth_price_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<UserBandwidthPrice, Error> {
        let _store: Arc<UserStorage> = self.store.clone();

        match _store
            .clone()
            .get_bandwidth_price_by_user_addr(user_addr)
            .await
        {
            Ok(bandwidth_price) => return Ok(bandwidth_price),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn update_bandwidth_price_of(
        self: Arc<Self>,
        user_addr: String,
        rate_per_kb: i64,
        rate_per_second: i64,
    ) -> Result<(), Error> {
        let _store = self.store.clone();

        match _store
            .clone()
            .update_bandwidth_price(user_addr.clone(), rate_per_kb, rate_per_second)
            .await
        {
            Ok(_) => {
                let price = UserBandwidthPrice {
                    user_addr,
                    rate_per_kb,
                    rate_per_second,
                };
                if let Err(e) = self
                    .clone()
                    .redis_svc
                    .clone()
                    .publish_peer_price(price.clone())
                    .await
                {
                    error!("failed to publish peer price price={:?} err={}", price, e);
                }
                return Ok(());
            }
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_referrals_overview_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<ReferralsOverview, Error> {
        let _store: Arc<UserStorage> = self.store.clone();

        match _store.clone().get_referrals_overview_of(user_addr).await {
            Ok(referrals_overview) => return Ok(referrals_overview),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_referral_of(self: Arc<Self>, user_addr: String) -> Result<Referral, Error> {
        let _store = self.store.clone();
        match _store.clone().get_referral_of(user_addr).await {
            Ok(result) => return Ok(result),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_referrals_by(self: Arc<Self>, user_addr: String) -> Result<Vec<Referral>, Error> {
        let _store = self.store.clone();
        // println!("get_referral_by_me");
        match _store.clone().get_referrals_by(user_addr).await {
            Ok(result) => return Ok(result),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn create_referral_code_of(
        self: Arc<Self>,
        user_addr: String,
        referral_code: String,
    ) -> Result<String, Error> {
        let _store = self.store.clone();

        match _store
            .clone()
            .create_referral_code(user_addr, referral_code)
            .await
        {
            Ok(result) => return Ok(result),
            Err(err) => return Err(Error::msg(format!("{}", err))),
        };
    }

    async fn import_referral_code_of(
        self: Arc<Self>,
        user_addr: String,
        friend_referral_code: String,
    ) -> Result<(), Error> {
        let _store = self.store.clone();

        match _store
            .clone()
            .import_referral_code(user_addr.clone(), friend_referral_code.clone())
            .await
        {
            Ok(_) => {
                // publish event
                let rerferrer_addr = _store
                    .clone()
                    .get_referrer_addr_by_referral_code(friend_referral_code)
                    .await?;
                let referral_extra = ReferralExtra {
                    referrer_addr: rerferrer_addr,
                    referee_addr: user_addr,
                };
                let path = format!("{}/{}", APP_CONFIG.accounting_service_url, "api/user/referral/link");
                // replace with create request insted of publish event
                let client = reqwest::Client::new();
                match client
                    .post(path)
                    .json(&referral_extra)
                    .send()
                    .await {
                        Ok(res) => {
                            if res.status().is_success() {
                                info!("referral code imported");
                                Ok(())
                            } else {
                                let status = res.status();
                                error!("referral code import failed with status code: {}", res.text().await.unwrap());
                                Err(Error::msg(format!("{}", status)))
                            }

                        },
                        Err(err) => Err(Error::msg(format!("{}", err))),
                    }

                // match self
                //     .clone()
                //     .publisher_svc
                //     .clone()
                //     .publish_dpn_event(referral_extra.clone())
                //     .await
                // {
                //     Ok(_) => Ok(()),
                //     Err(err) => Err(Error::msg(format!("{}", err))),
                // }
            }
            Err(err) => Err(Error::msg(format!("{}", err))),
        }
    }

    async fn withdraw_reward(
        self: Arc<Self>,
        user_addr: String,
        withdrawal_addr: String,
    ) -> Result<(), Error> {
        let withdrawal_event = DPNEvent::Withdrawal(WithdrawalExtra {
            user_addr,
            withdrawal_addr,
        });
        match self
            .publisher_svc
            .clone()
            .publish_dpn_event(withdrawal_event)
            .await
        {
            Ok(_) => Ok(()),
            Err(err) => Err(Error::msg(format!("{}", err))),
        }
    }

    async fn get_total_pending_withdrawal_txs_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<i64, Error> {
        let _store: Arc<UserStorage> = self.store.clone();

        match _store
            .clone()
            .get_total_pending_withdrawal_txs(user_addr)
            .await
        {
            Ok(total_pending) => return Ok(total_pending),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_withdrawal_addr_of(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Option<Address>> {
        self.clone()
            .store
            .clone()
            .get_withdrawal_addr_of(user_addr)
            .await
    }

    async fn get_withdrawals_of(self: Arc<Self>, user_addr: String) -> anyhow::Result<Vec<Tx>> {
        let withdrawal_txs = self
            .clone()
            .store
            .clone()
            .get_withdrawals_of(user_addr)
            .await?;
        Ok(withdrawal_txs.iter().map(|w| (w.clone()).into()).collect())
    }

    async fn update_withdrawal_addr_of(
        self: Arc<Self>,
        user_addr: String,
        withdrawal_addr: Address,
    ) -> anyhow::Result<()> {
        self.clone()
            .store
            .clone()
            .update_withdrawal_addr_of(user_addr, withdrawal_addr)
            .await?;
        Ok(())
    }

    async fn update_proxy_account(
        self: Arc<Self>,
        user_addr: String,
        proxy_acc_request: ProxyAccUpdate,
    ) -> Result<(), Error> {
        match self
            .store
            .clone()
            .update_proxy_account(user_addr, proxy_acc_request)
            .await
        {
            Ok(proxy_acc_data) => {
                // hides password
                let mut proxy_acc_data = proxy_acc_data.clone();
                proxy_acc_data.password =
                    bytes_to_hex_string(hash(proxy_acc_data.password.as_bytes()).as_bytes());

                let changed = ProxyAccChanged::Updated(proxy_acc_data.clone());
                if let Err(e) = self
                    .clone()
                    .redis_svc
                    .clone()
                    .publish_proxy_acc(changed.clone())
                    .await
                {
                    error!("failed to publish proxy acc={:?} err={}", changed, e);
                }

                Ok(())
            }
            Err(e) => return Err(Error::msg(format!("{}", e))),
        }
    }

    async fn delete_proxy_account(
        self: Arc<Self>,
        user_addr: String,
        proxy_acc_id: String,
    ) -> Result<(), Error> {
        match self
            .store
            .clone()
            .delete_proxy_acc(user_addr, proxy_acc_id.clone())
            .await
        {
            Ok(_) => {
                let changed = ProxyAccChanged::Deleted(proxy_acc_id.clone());
                if let Err(e) = self
                    .clone()
                    .redis_svc
                    .clone()
                    .publish_proxy_acc(changed.clone())
                    .await
                {
                    error!("failed to publish proxy change={:?} err={}", changed, e);
                }
                return Ok(());
            }
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }

    async fn get_user_tier_points(
        self: Arc<Self>,
        user_addr: String,
    ) -> anyhow::Result<Vec<TierPoint>> {
        self.clone()
            .store
            .clone()
            .get_user_tier_points(user_addr)
            .await
    }

    async fn add_user_tier_points(
        self: Arc<Self>,
        user_addr: String,
        points: i64,
    ) -> anyhow::Result<()> {
        self.clone()
            .store
            .clone()
            .add_user_tier_points(user_addr, points)
            .await?;
        Ok(())
    }

    async fn get_client_proxy_accounts(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<ProxyAccDto>, Error> {
        let client_proxies = self
            .clone()
            .store
            .clone()
            .get_proxy_accounts_by_user_addr(user_addr)
            .await?;
        Ok(client_proxies)
    }

    async fn get_client_overview(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<ClientOverview, Error> {
        let client_overview = self
            .clone()
            .store
            .clone()
            .get_client_overview(user_addr)
            .await?;
        Ok(client_overview)
    }

    async fn register_notification(
        self: Arc<Self>,
        noti_register: NotificationRegister,
    ) -> Result<(), Error> {
        match self
            .publisher_svc
            .clone()
            .publish_notification_event(NotificationEvent::Register(noti_register))
            .await
        {
            Ok(_) => Ok(()),
            Err(err) => Err(Error::msg(format!("{}", err))),
        }
    }

    async fn get_online_sessions_of_user(
        self: Arc<Self>,
        user_addr: String,
    ) -> Result<Vec<UserOnlineSession>, Error> {
        let user_online_sessions = self
            .clone()
            .store
            .clone()
            .get_online_sessions_of_user(user_addr.clone())
            .await?;

        let storage_user_online_sessions: Vec<UserOnlineSession> = user_online_sessions.clone().iter().map(|u| UserOnlineSession {
                user_addr: u.user_addr.clone(),
                earned_lp: u.earned_lp.clone(),
                start_time: u.start_time.clone(),
                end_time: u.end_time.clone(),
                created_at: u.created_at.clone(),
                updated_at: u.updated_at.clone(),
            }).collect();

        Ok(storage_user_online_sessions)
    }

    async fn get_user_by_pincode(self: Arc<Self>, pincode: String) -> Result<Option<User>, Error> {
        let _store = self.store.clone();
        match _store.clone().get_user_by_pincode(pincode).await {
            Ok(result) => return Ok(result),
            Err(e) => return Err(Error::msg(format!("{}", e))),
        };
    }
}

impl UserServiceImpl {
    pub async fn new(
        redis_svc: Arc<RedisService>,
        user_store: Arc<UserStorage>,
        publisher_svc: Arc<dyn PublisherService>,
    ) -> Result<Self, Error> {
        let (shutdown_tx, shutdown_rx) = tokio::sync::mpsc::unbounded_channel::<()>();

        // Initialize the email cache with a 5-minute time-to-live
        let email_cache = Cache::builder()
            .time_to_live(Duration::from_secs(300)) // 5 minutes TTL
            .build();

        Ok(Self {
            redis_svc,
            store: user_store,
            publisher_svc,
            shutdown_tx,
            shutdown_rx: Mutex::new(shutdown_rx),
            email_cache,
        })
    }

    pub async fn stop(self: Arc<Self>) -> Result<(), Error> {
        info!("shutting down user service...");
        let _self = self.clone();
        let _self2 = self.clone();
        _ = _self.shutdown_tx.send(());
        Ok(())
    }

    pub async fn run(self: Arc<Self>) -> Result<(), Error> {
        let _self = self.clone();
        let _self2 = self.clone();
        let mut shutdown_rx = _self2.shutdown_rx.lock().await;

        tokio::select! {
            _ =  shutdown_rx.recv() => {
                info!("service stopped");
                Ok(())
            },
        }
    }
}
