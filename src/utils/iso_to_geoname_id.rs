use std::fs::File;
use anyhow::Result;
use std::io::{ <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::sync::Arc;
use std::collections::HashMap;

#[derive(Debug)]
pub struct IsoToGeonameIdService {
    mappings: HashMap<String, u32>,
}

impl IsoToGeonameIdService {
    pub fn new(file_path: String) -> Result<Self> {
        let file = File::open(file_path)?;
        let reader = BufReader::new(file);
        let mut mappings = HashMap::new();

        for line in reader.lines() {
            let line = line?;
        
        // Skip comment lines
        if line.starts_with('#') {
            continue;
        }
        
        let fields: Vec<&str> = line.split('\t').collect();
        
        if fields.len() >= 17 {
            // ISO code is in fields[0], GeoName ID is in fields[16]
            let iso_code = fields[0].to_string();
            
            if let Ok(geoname_id) = fields[16].parse::<u32>() {
                mappings.insert(iso_code, geoname_id);
            }
            }
        }
        Ok(Self { mappings })
    }

    pub fn iso_code_to_geoname_id_complete(self: Arc<Self>, iso_code_2: &str) -> Option<u32> {
        // Convert the input to uppercase for case-insensitive matching
        let iso_code_upper = iso_code_2.to_uppercase();

        // Try to find the country in the complete mapping
        if let Some(id) = self.mappings.get(&iso_code_upper) {
            return Some(*id);
        } 

        // Fall back to the basic mapping if not found
        //iso_code_to_geoname_id(&iso_code_upper)
        None
    }
}
