use crate::bootnode::utils::get_geo_from_ip_address;
use crate::webapi::AppState;
use actix_web::{get, put, web, HttpMessage, HttpRequest, HttpResponse, Result};
use dpn_core::types::{api::ErrorWrapper,
    bandwidth::UserBandwidthPrice, auth::UserClaims, bandwidth::SessionStatus};
use ethers::types::Address;
use log::error;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::net::IpAddr;
use std::sync::Arc;
use utoipa::ToSchema;

pub fn init_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(overview);
    cfg.service(connection_history);
    cfg.service(get_session_by_hash);
    cfg.service(suggested_bandwidth_price);
    cfg.service(get_bandwidth_price);
    cfg.service(update_bandwidth_price);
    cfg.service(assign_masternode);
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct ConnectionOverview {
    pub total_connections: i64,
    /// in szabo
    pub total_rewards: i64,
    /// in kilobytes
    pub total_bandwidth_usages: i64,
    /// in szabo
    pub unclaimed_rewards: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct SessionDetails {
    pub session_hash: String,
    pub provider_addr: String,
    pub client_addr: String,
    /// szabo
    pub rate_per_kb_v2: i64,
    /// szabo
    pub rate_per_kb: i64,
    pub handshake_at: Option<i64>,
    pub end_at: Option<i64>,
    /// epoch seconds
    pub duration: Option<i64>,
    /// in kilobytes
    pub bandwidth_usage: Option<i64>,
    /// szabo
    pub bandwidth_fee: i64,
    /// szabo
    pub total_fee: i64,
    /// szabo
    pub total_fee_v2: i64,
    pub status: SessionStatus,
    
    // this field current hold data platform_fee 
    pub duration_fee : i64 
}


// pub fn session_from_core_to_api(session: SessionCore) -> Session {
//     Session {
//         session_hash: bytes_to_hex_string(session.session_hash.clone().as_bytes()),
//         provider_addr: bytes_to_hex_string(session.provider_addr.clone().as_bytes()),
//         client_addr: bytes_to_hex_string(session.client_addr.clone().as_bytes()),
//         rate_per_second: u256_to_szabo(session.rate_per_second.clone()),
//         rate_per_kb: u256_to_szabo(session.rate_per_kb.clone()),
//         handshake_at: session.handshake_at.clone(),
//         end_at: session.end_at.clone(),
//         duration: session.duration.clone(),
//         bandwidth_usage: session.bandwidth_usage.clone(),
//         duration_fee: u256_to_szabo(session.duration_fee.clone()),
//         bandwidth_fee: u256_to_szabo(session.bandwidth_fee.clone()),
//         total_fee: u256_to_szabo(session.total_fee.clone()),
//         status: session.status.clone(),
//     }
// }

#[utoipa::path(
    get,
    path = "/connections/overview",
    tag = "Connection",
    responses(
        (status=200, description="Get connection overview successfully!", body=ConnectionOverviewV2),
        (status=500, description="Failed to fetch connection overview", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/overview")]
pub async fn overview(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let accounting_svc = app_state.accounting_svc.clone();
    match accounting_svc.get_connection_overview(user_addr).await {
        Ok(conn_overview) => return Ok(HttpResponse::Ok().json(conn_overview).into()),
        Err(_) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                "cannot get connection overview",
            )
            .build())
        }
    }
}

#[utoipa::path(
    get,
    path = "/connections/connection_history",
    tag = "Connection",
    responses(
        (status=200, description= "Get active connection successfully!", body = [Session]),
        (status=500, description= "Failed to fetch session history", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/connection_history")]
pub async fn connection_history(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let accounting_svc = app_state.accounting_svc.clone();

    match accounting_svc
        .get_connection_history(user_addr.clone())
        .await
    {
        Ok(sessions) => {
            // cast to SessionDetails
            let sessions: Vec<SessionDetails> = sessions
                .iter()
                .map(|s| SessionDetails {
                    session_hash: s.session_hash.clone(),
                    provider_addr: s.provider_addr.clone(),
                    client_addr: s.client_addr.clone(),
                    rate_per_kb_v2: s.rate_per_kb_v2,
                    rate_per_kb: s.rate_per_kb,
                    handshake_at: s.handshake_at,
                    end_at: s.end_at,
                    duration: s.duration,
                    bandwidth_usage: s.bandwidth_usage,
                    bandwidth_fee: s.bandwidth_fee,
                    total_fee: s.total_fee,
                    total_fee_v2: s.total_fee_v2,
                    status: s.status,
                    duration_fee: s.duration_fee,
                })
                .collect();
            return Ok(HttpResponse::Ok().json(sessions));
        }
        Err(e) => {
            error!("cannot get session history: {}", e);
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                "cannot get session history",
            )
            .build());
        }
    }
}

#[utoipa::path(
    get,
    path = "/connections/detail/{hash}",
    tag = "Connection",
    responses(
        (status=200, description= "Get session detail successfully!", body = Session),
        (status=400, description= "Invalid session hash", body=ErrorWrapper),
        (status=401, description= "Unauthorized!", body=ErrorWrapper),
        (status=404, description= "Session not found!", body=ErrorWrapper),
        (status=500, description= "Failed to fetch session detail", body=ErrorWrapper),
    ),
    params(
        ("hash" = String, Path, description = "Session hash")
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/detail/{hash}")]
pub async fn get_session_by_hash(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let _user_addr: Address = {
        match user_claims.user_addr.clone().parse::<Address>() {
            Ok(addr) => addr,
            Err(_) => {
                return Ok(ErrorWrapper::builder(
                    StatusCode::UNAUTHORIZED,
                    "invalid user address format",
                )
                .build())
            }
        }
    };
    let accounting_svc = app_state.accounting_svc.clone();
    match req.match_info().query("hash").parse::<String>() {
        Ok(hash) => match accounting_svc.get_session_by_hash(&hash).await {
            Ok(Some(_session)) => {
                // if session.provider_addr != user_addr && session.client_addr != user_addr {
                //     return Ok(ErrorWrapper::builder(
                //         StatusCode::UNAUTHORIZED,
                //         "unauthorized to get session detail",
                //     )
                //     .build());
                // }
                return Ok(HttpResponse::Ok().json(_session));
            }
            Ok(None) => {
                return Ok(
                    ErrorWrapper::builder(StatusCode::NOT_FOUND, "session not found").build(),
                )
            }
            Err(_) => {
                return Ok(ErrorWrapper::builder(
                    StatusCode::INTERNAL_SERVER_ERROR,
                    "cannot get session detail",
                )
                .build())
            }
        },
        Err(_) => {
            return Ok(
                ErrorWrapper::builder(StatusCode::BAD_REQUEST, "cannot get session id").build(),
            )
        }
    }
}

#[utoipa::path(
    get,
    path = "/connections/suggested_bandwidth_price",
    tag = "Connection",
    responses(
        (status=200, body=BandwidthPrice),
        (status=500, body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/suggested_bandwidth_price")]
pub async fn suggested_bandwidth_price(
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let conn_svc = app_state.connection_svc.clone();

    let suggested_bandwidth_price = conn_svc.get_suggested_bandwidth_price().await;
    // TODO(rameight): temporarily put the bandwidth price field to to endpoint to fix UI
    Ok(HttpResponse::Ok().json(json!({
        "suggested_bandwidth_price": suggested_bandwidth_price.rate_per_kb,
        "rate_per_kb": suggested_bandwidth_price.rate_per_kb,
        "rate_per_second": suggested_bandwidth_price.rate_per_second,
    })))
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct BandwidthPrice {
    pub rate_per_kb: i64,
    pub rate_per_second: i64,
}

#[derive(Deserialize, Debug)]
pub struct BandWidthReq {
    pub up: u64,
    pub down: u64,
    pub p_id: String,
    pub p_ip: IpAddr,
    pub c_up: u64,
    pub c_down: u64,
    pub c_uname: String,
}

#[utoipa::path(
    put,
    path = "/connections/bandwidth-price",
    tag = "Connection",
    request_body(content = BandwidthPrice, description = "Update Bandwidth Price"),
    responses(
        (status=201, description= "Update bandwidth price successfully!", body=BandwidthPrice),
        (status=400, description= "Invalid bandwidth price", body=ErrorWrapper),
        (status=500, description= "Update bandwidth price failed", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[put("/bandwidth-price")]
pub async fn update_bandwidth_price(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<BandwidthPrice>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    let mut bandwidth_price: BandwidthPrice = body.0;
    // TODO(rameight): remove this hardcode until price matching issue is resolved
    bandwidth_price.rate_per_kb = 50;
    bandwidth_price.rate_per_second = 0;

    if bandwidth_price.rate_per_kb < 0
        || bandwidth_price.rate_per_second < 0
        || bandwidth_price.rate_per_kb > 1000000000000 // 1000000 U2U / GB
        || bandwidth_price.rate_per_second > (278 * 1000000000000)
    // 1000000 U2U / hour
    {
        return Ok(
            ErrorWrapper::builder(StatusCode::BAD_REQUEST, "invalid bandwidth price").build(),
        );
    }

    match user_svc
        .update_bandwidth_price_of(
            user_addr.clone(),
            bandwidth_price.rate_per_kb,
            bandwidth_price.rate_per_second,
        )
        .await
    {
        Ok(_) => Ok(HttpResponse::Ok().json(bandwidth_price)),
        Err(_) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                "cannot update bandwidth price",
            )
            .build())
        }
    }
}

#[utoipa::path(
    get,
    path = "/connections/bandwidth-price",
    tag = "Connection",
    responses(
        (status=200, description= "Fetch bandwidth price successfully!", body=UserBandwidthPrice),
        (status=400, description= "Update bandwidth price failed", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/bandwidth-price")]
pub async fn get_bandwidth_price(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    let user_svc = app_state.user_svc.clone();
    let conn_svc = app_state.connection_svc.clone();

    match user_svc.get_bandwidth_price_of(user_addr).await {
        Ok(mut user_bandwidth_price) => {
            if user_bandwidth_price.rate_per_kb == 0 && user_bandwidth_price.rate_per_second == 0 {
                let suggested_prices = conn_svc.get_suggested_bandwidth_price().await;
                user_bandwidth_price = UserBandwidthPrice {
                    user_addr: user_bandwidth_price.user_addr.clone(),
                    rate_per_kb: suggested_prices.rate_per_kb,
                    rate_per_second: suggested_prices.rate_per_second,
                };
            }

            Ok(HttpResponse::Ok().json(user_bandwidth_price))
        }
        Err(_) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                "cannot get user bandwidth price",
            )
            .build())
        }
    }
}

#[utoipa::path(
    get,
    path = "/connections/assign_masternode",
    tag = "Connection",
    responses(
        (status=200, description= "Get assign masternode successfully", body = AssignMasternodeRes),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/assign_masternode")]
pub async fn assign_masternode(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> actix_web::Result<HttpResponse> {
    let geoname_id = match req.connection_info().realip_remote_addr() {
        Some(peer_ip_addr) => match get_geo_from_ip_address(peer_ip_addr.to_string()) {
            // Ok(geo) => geo
            //     .continent
            //     .and_then(|c| c.code)
            //     .unwrap_or(DEFAULT_CONTINENTAL_CODE.to_string()),
            // Err(_) => DEFAULT_CONTINENTAL_CODE.to_string(),
        
            Ok(geo) => geo.country.and_then(|c| c.geoname_id).unwrap_or(0),
            Err(_) => 0,
        },

        None => 0,
    };


    let rs = app_state
        .bootnode_svc
        .clone()
        .assign_masternode(geoname_id)
        .await;

    #[derive(Serialize)]
    struct MasternodeInfoV0 {
        region: String,
        peer_bind: String,
        client_bind: String,
        control_bind: String,
        web_bind: String,
        root_ca: Option<String>,
    }

    #[derive(Serialize)]
    struct AssignMasternodeResV0 {
        masternode: Option<MasternodeInfoV0>,
    }

    let masternode_info = rs.map(|i| MasternodeInfoV0 {
        region: "vn".to_string(),
        peer_bind: i.peer_bind,
        client_bind: i.client_bind,
        control_bind: i.control_bind,
        web_bind: i.web_bind,
        root_ca: i.root_ca,
    });

    return Ok(HttpResponse::Ok().json(AssignMasternodeResV0 {
        masternode: masternode_info,
    }));
}
