use std::sync::Arc;
use actix_web::{get, web, HttpRequest, HttpResponse, Result};
use dpn_core::types::api::ErrorWrapper;
use reqwest::StatusCode;
use serde_json::json;
use crate::{bootnode::utils::get_geo_from_ip_address, webapi::AppState};


pub fn init_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(get_bonus_info);
    cfg.service(get_all_bonus_info);
    // cfg.service(get_continents);
    // cfg.service(get_countries);
    // cfg.service(get_countries_by_continent);
}

#[utoipa::path(
    get,
    path = "/locations/bonus-info",
    tag = "Locations",
    responses(
        (status=200, description= "Get bonus info successfully!"),
        (status=500, description= "Server return unexpected error", body=ErrorWrapper),
    )
)]
#[get("/bonus-info")]
pub async fn get_bonus_info(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    match req.connection_info().realip_remote_addr() {
        Some(ip_addr) => {
            let geo = get_geo_from_ip_address(ip_addr.to_string());
            match geo {
                Ok(geo) => {
                    let location_svc = app_state.location_svc.clone();
                    let bonus_info = location_svc.get_bonus_info_by_country(
                        geo.country.clone().unwrap().geoname_id.unwrap_or_default() as i32
                    ).await;
                    match bonus_info {
                        Ok(bonus_info) => {
                            return Ok(HttpResponse::Ok().json(json!({
                                "country_name": geo.country.unwrap_or_default().name,
                                "bonus_info": bonus_info,
                            })));
                        }
                        Err(e) => {
                            return Ok(ErrorWrapper::builder(
                                StatusCode::BAD_REQUEST,
                                &format!("cannot get bonus info err= {}", e),
                            )
                            .build());
                        }
                    }
                }
                Err(e) => {
                    return Ok(ErrorWrapper::builder(
                        StatusCode::BAD_REQUEST,
                        &format!("cannot get geo from ip address err= {}", e),
                    )
                    .build());
                }
            }
        }
        None => {
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                "cannot get client ip address",
            )
            .build());
        }
    }
}

#[utoipa::path(
    get,
    path = "/locations/bonus-info/all",
    tag = "Locations",
    responses(
        (status=200, description= "Get all bonus info successfully!"),
        (status=500, description= "Server return unexpected error", body=ErrorWrapper),
    )
)]
#[get("/bonus-info/all")]
pub async fn get_all_bonus_info(
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let location_svc = app_state.location_svc.clone();
    let bonus_info = location_svc.get_all_bonus_info().await;
    match bonus_info {
        Ok(bonus_info) => Ok(HttpResponse::Ok().json(bonus_info)),
        Err(e) => Ok(ErrorWrapper::builder(
            StatusCode::BAD_REQUEST,
            &format!("cannot get all bonus info err= {}", e),
        )
        .build()),
    }
}

// #[utoipa::path(
//     get,
//     path = "/locations/continents",
//     tag = "Locations",
//     responses(
//         (status=200, description= "Get continent data successfully!", body=[Continent]),
//         (status=500, description= "Server return unexpected error", body=ErrorWrapper),
//     )
// )]
// #[get("/continents")]
// pub async fn get_continents(
//     app_state: web::Data<Arc<AppState>>,
// ) -> Result<HttpResponse> {
//     let location_svc = app_state.location_svc.clone();
//     match location_svc.get_continents().await{
//         Ok(continents) => Ok(HttpResponse::Ok().json(continents)),
//         Err(e) => Ok(ErrorWrapper::builder(
//             StatusCode::BAD_REQUEST,
//             &format!("cannot get continents data err= {}", e),
//         )
//         .build()),
//     }
// }

// #[utoipa::path(
//     get,
//     path = "/locations/countries",
//     tag = "Locations",
//     responses(
//         (status=200, description= "Get countries data successfully!", body=[LocationCountry]),
//         (status=500, description= "Server return unexpected error", body=ErrorWrapper),
//     )
// )]
// #[get("/countries")]
// pub async fn get_countries(
//     app_state: web::Data<Arc<AppState>>,
// ) -> Result<HttpResponse> {
//     let location_svc = app_state.location_svc.clone();
//     match location_svc.get_countries().await{
//         Ok(countries) => Ok(HttpResponse::Ok().json(countries)),
//         Err(e) => Ok(ErrorWrapper::builder(
//             StatusCode::BAD_REQUEST,
//             &format!("cannot get continents data err= {}", e),
//         )
//         .build()),
//     }
// }

// #[utoipa::path(
//     get,
//     path = "/locations/continents/{geoname_id}/countries",
//     params(
//         ("geoname_id" = Integer, Path, description = "Continent geoname id", example = 6255149, format = "integer")
//     ),
//     tag = "Locations",
//     responses(
//         (status=200, description= "Get countries data successfully!", body=[Country]),
//         (status=400, description= "Invalid continent", body = ErrorWrapper),
//         (status=500, description= "Server return unexpected error", body=ErrorWrapper),
//     )
// )]
// #[get("/continents/{geoname_id}/countries")]
// pub async fn get_countries_by_continent(
//     req: HttpRequest,
//     app_state: web::Data<Arc<AppState>>,
// ) -> Result<HttpResponse> {
//     let geoname_id: i32 = req.match_info().get("geoname_id").unwrap().parse().unwrap();
//     let valid_continents = vec![6255146, 6255147, 6255148, 6255149, 6255151, 6255150];
//     if !valid_continents.contains(&geoname_id){
//         return Ok(ErrorWrapper::builder(
//             StatusCode::BAD_REQUEST,
//             &format!("invalid continent code: {}", geoname_id),
//         )
//         .build());
//     }

//     let location_svc = app_state.location_svc.clone();
//     match location_svc.get_countries_by_continent(geoname_id).await{
//         Ok(countries) => Ok(HttpResponse::Ok().json(countries)),
//         Err(e) => Ok(ErrorWrapper::builder(
//             StatusCode::BAD_REQUEST,
//             &format!("cannot get countries data err= {}", e),
//         )
//         .build()),
//     }
// }

