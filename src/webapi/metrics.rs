use crate::webapi::AppState;
use actix_web::{get, web, HttpResponse, Result};
use dpn_core::types::api::ErrorWrapper;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use utoipa::ToSchema;

pub fn init_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(health);
    cfg.service(online_masternodes);
    cfg.service(online_peers);
    cfg.service(masternode_metrics);
}

#[utoipa::path(
    get,
    path = "/metrics/health",
    tag = "Metrics",
    responses(
        (status=200, description= "Admin service is online!"),
    )
)]
#[get("/health")]
pub async fn health() -> actix_web::Result<HttpResponse> {
    return Ok(HttpResponse::Ok().into());
}

#[utoipa::path(
    get,
    path = "/metrics/online_peers",
    tag = "Metrics",
    responses(
        (status=200, description= "Retrieve online peers successfully!", body=Vec<PeernodeInfo>),
    )
)]
#[get("/online_peers")]
pub async fn online_peers(app_state: web::Data<Arc<AppState>>) -> Result<HttpResponse> {
    let masternode_svc = app_state.bootnode_svc.clone();

    return match masternode_svc.online_peers().await {
        Ok(online_peers) => Ok(HttpResponse::Ok().json(online_peers)),
        Err(_) => Ok(HttpResponse::BadRequest().into()),
    };
}

#[utoipa::path(
    get,
    path = "/metrics/online_masternodes",
    tag = "Metrics",
    responses(
        (status=200, description= "Fetch online masternode successfully!", body = Vec<MasternodeInfo>),
        (status=400, description= "Fetch online masternode Failed"),
    )
)]
#[get("/online_masternodes")]
pub async fn online_masternodes(
    app_state: web::Data<Arc<AppState>>,
) -> actix_web::Result<HttpResponse> {
    let masternodes = app_state.bootnode_svc.clone().online_masternodes().await;
    return Ok(HttpResponse::Ok().json(masternodes));
}

#[utoipa::path(
    get,
    path = "/metrics/masternode-metrics",
    tag = "Metrics",
    responses(
        (status=200, description= "Fetch masternode metrics successfully!", body = Vec<MasternodeMetric>),
        (status=400, description= "Fetch masternode metrics Failed"),
    )
)]
#[get("/masternode-metrics")]
pub async fn masternode_metrics(
    app_state: web::Data<Arc<AppState>>,
) -> actix_web::Result<HttpResponse> {
    match app_state.bootnode_svc.clone().masternode_metrics().await {
        Ok(masternode_metrics) => Ok(HttpResponse::Ok().json(masternode_metrics)),
        Err(_) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                "Error when get masternode metrics",
            )
            .build())
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct MasternodeMetric {
    pub masternode_ip_addr: String,
    pub active_peers: u32,
    pub active_clients: u32,
}
