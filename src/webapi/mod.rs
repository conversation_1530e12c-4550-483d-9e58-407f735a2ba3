use crate::bootnode::BootnodeService;
use crate::connection::ConnectionService;
use crate::integration::accounting::AccountingService;
use crate::integration::loyalty_points::LoyaltyPointsService;
use crate::location::LocationService;
use crate::monitoring::metrics::{HTTP_REQUESTS_TOTAL, HTTP_RESPONSE_TIME_SECONDS};
use crate::user::UserService;
use crate::APP_CONFIG;
use actix_web::dev::{Service, ServiceResponse};
use actix_web::*;
use actix_web_httpauth::middleware::HttpAuthentication;
use dpn_core::types::{referral::Referral, tier::UserTier, user::User};
use futures_util::future::ready;
use moka::future::Cache;
use prometheus::HistogramTimer;
use std::sync::Arc;
use std::time::Duration;
use utoipa::OpenApi;
use utoipa_rapidoc::RapiDoc;
use utoipa_redoc::{Redoc, Servable};
use utoipa_swagger_ui::SwaggerUi;
use clients::HealthCheckResponse;

pub mod auth;
pub mod clients;
pub mod connections;
pub mod masternode;
pub mod metrics;
pub mod monitoring;
pub mod swagger;
pub mod users;
pub mod location;

// Define a struct to hold the user detail cache data
#[derive(Clone)]
pub struct UserDetailCache {
    pub user: Option<User>,
    pub user_tier: Option<UserTier>,
    pub user_referral: Option<Referral>,
    pub last_updated: i64,
}

#[derive(Clone)]
pub struct AppState {
    pub user_svc: Arc<dyn UserService>,
    pub connection_svc: Arc<dyn ConnectionService>,
    pub bootnode_svc: Arc<dyn BootnodeService>,
    pub accounting_svc: Arc<dyn AccountingService>,
    pub location_svc: Arc<dyn LocationService>,
    pub loyalty_points_svc: Arc<dyn LoyaltyPointsService>,
    pub user_detail_cache: Cache<String, UserDetailCache>,
    pub proxy_health_check_cache: Cache<String,HealthCheckResponse>,
}

pub async fn run_web(
    user_svc: Arc<dyn UserService>,
    connection_svc: Arc<dyn ConnectionService>,
    bootnode_svc: Arc<dyn BootnodeService>,
    accounting_svc: Arc<dyn AccountingService>,
    location_svc: Arc<dyn LocationService>,
    loyalty_points_svc: Arc<dyn LoyaltyPointsService>,
) -> anyhow::Result<()> {
    // Initialize the user detail cache with a 5-minute time-to-live
    let user_detail_cache = Cache::builder()
        .time_to_live(Duration::from_secs(300)) // 5 minutes TTL
        .build();
    let proxy_health_check_cache = Cache::builder()
        .time_to_live(Duration::from_secs(5)) // 5 seconds TTL
        .build(); 
    let app_state = Arc::new(AppState {
        user_svc,
        bootnode_svc,
        connection_svc,
        accounting_svc,
        location_svc,
        loyalty_points_svc,
        user_detail_cache,
        proxy_health_check_cache,
    });

    HttpServer::new(move || {
        let cors = actix_cors::Cors::permissive();
        let auth_middleware = HttpAuthentication::bearer(auth::verify_jwt);
        let app_data = web::Data::new(app_state.clone());
        App::new()
            .wrap_fn(|req, srv| {
                let mut histogram_timer: Option<HistogramTimer> = None;
                let request_path = req.path();
                let is_registered_resource = req.resource_map().has_resource(request_path);
                // this check prevents possible DoS attacks that can be done by flooding the application
                // using requests to different unregistered paths.
                if is_registered_resource {
                    let request_method = req.method().to_string();
                    histogram_timer = Some(
                        HTTP_RESPONSE_TIME_SECONDS
                            .with_label_values(&[&request_method, request_path])
                            .start_timer(),
                    );
                    HTTP_REQUESTS_TOTAL
                        .with_label_values(&[&request_method, request_path])
                        .inc();
                }

                let fut = srv.call(req);

                async {
                    let res = fut.await?;
                    if let Some(histogram_timer) = histogram_timer {
                        histogram_timer.observe_duration();
                    };
                    Ok(res)
                }
            })
            // .wrap_fn(monitoring_middleware)
            .wrap(cors)
            .wrap(middleware::NormalizePath::trim())
            .app_data(web::Data::new(app_state.clone()).clone())
            .service(
                web::scope("/auth")
                    .configure(auth::init_routes)
                    .app_data(app_data.clone()),
            )
            .service(
                web::scope("/masternode")
                    .configure(masternode::init_routes)
                    .wrap_fn(|req, srv| {
                        let headers = req.headers();
                        let x_key_api = headers
                            .get("x-api-key")
                            .and_then(|value| value.to_str().ok());
                        match x_key_api {
                            Some(x_key_api_value) => {
                                if &x_key_api_value == &APP_CONFIG.x_api_key {
                                    srv.call(req)
                                } else {
                                    Box::pin(ready(Ok(ServiceResponse::new(
                                        req.into_parts().0,
                                        HttpResponse::Unauthorized().finish(),
                                    ))))
                                }
                            }
                            None => Box::pin(ready(Ok(ServiceResponse::new(
                                req.into_parts().0,
                                HttpResponse::Unauthorized().finish(),
                            )))),
                        }
                    })
                    .app_data(app_data.clone()),
            )
            .service(
                web::scope("/users")
                    .configure(users::init_routes)
                    .wrap(auth_middleware.clone())
                    .app_data(app_data.clone()),
            )
            .service(
                web::scope("/clients")
                    .configure(clients::init_routes)
                    .wrap(auth_middleware.clone())
                    .app_data(app_data.clone()),
            )
            .service(
                web::scope("/connections")
                    .configure(connections::init_routes)
                    .wrap(auth_middleware.clone())
                    .app_data(app_data.clone()),
            )
            .service(
                web::scope("/locations")
                    .configure(location::init_routes)
                    .app_data(app_data.clone()),
            )
            .service(
                web::scope("/metrics")
                    .configure(metrics::init_routes)
                    .app_data(app_data.clone()),
            )
            .service(
                web::scope("/monitoring")
                    .configure(monitoring::init_routes)
                    .app_data(app_data.clone()),
            )
            .service(Redoc::with_url("/redoc", swagger::ApiDoc::openapi()))
            .service(RapiDoc::new("/api-docs/openapi.json").path("/rapidoc"))
            .service(
                SwaggerUi::new("/swagger-ui/{_:.*}")
                    .url("/api-docs/openapi.json", swagger::ApiDoc::openapi()),
            )
    })
    .bind(&APP_CONFIG.web_bind)?
    .run()
    .await?;

    Ok(())
}
