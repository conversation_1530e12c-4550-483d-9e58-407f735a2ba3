use actix_web::http::header::{self};
use actix_web::{get, web, HttpResponse, Result};
use prometheus::{Encoder, TextEncoder};

pub fn init_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(metrics);
}

#[get("/monitoring")]
pub async fn metrics() -> Result<HttpResponse> {
    let encoder = TextEncoder::new();
    let mut buffer = vec![];
    encoder
        .encode(&prometheus::gather(), &mut buffer)
        .expect("Failed to encode metrics");

    let response = String::from_utf8(buffer.clone()).expect("Failed to convert bytes to string");
    buffer.clear();

    Ok(HttpResponse::Ok()
        .insert_header(header::ContentType(mime::TEXT_PLAIN))
        .body(response))
}
