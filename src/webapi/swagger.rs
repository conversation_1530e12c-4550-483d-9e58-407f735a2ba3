use super::{
    clients::{ProxyAccDelete, ProxyAccInfo, ProxyAccUpdate, ClientOverview, HealthCheckResponse},
    connections::{BandwidthPrice, ConnectionOverview, SessionDetails},
    masternode::RegionInfoResponse,
    metrics::MasternodeMetric,
};
use crate::webapi::{auth, clients::{self, OnlinePeerByGeo, PeerByCountryStats,ForceStopPeerRequest}, connections, location, masternode, metrics, users};
use dpn_core::types::{api::ErrorWrapper, geo::{Continent, Country, City}};
use dpn_core::types::{
    bandwidth::{SessionStatus, UserBandwidthPrice},
    connection::{PrioritizedIPLevel, VerifyProxyAccData},
    masternode::{AssignMasternodeRes, MasternodeInfo},
    referral,
    region::{UserRegionInfo, UserRegionInfoHistory},
    reward::RewardsOverview,
    tier::{Tier, TierPoint, UserTier},
    tx::{TxStatus, TxType},
};
use dpn_db::proxy_acc_dal::ProxyAccDto;
use utoipa::{
    openapi::security::{ApiKey, ApiKeyValue, HttpAuthScheme, HttpBuilder, SecurityScheme},
    Modify, OpenApi,
};

#[derive(OpenApi)]
#[openapi(
    paths(
        auth::sso_sign_in,
        auth::refresh_token,
        auth::sign_up,
        auth::sign_in,
        auth::reset_password,
        connections::overview,
        connections::connection_history,
        connections::suggested_bandwidth_price,
        connections::update_bandwidth_price,
        connections::get_bandwidth_price,
        connections::get_session_by_hash,
        connections::assign_masternode,
        users::get_user_detail,
        users::get_referral_by_me,
        users::create_referral_code,
        users::import_referral_code,
        users::get_referrals_overview_of,
        users::claim_rewards,
        users::claim_rewards_history,
        users::rewards_overview,
        users::update_withdrawal_address,
        users::get_total_pending_withdrawal_txs,
        users::get_user_tier_points,
        users::register_notification,
        users::get_online_sessions_of_user,
        clients::create_algo_proxy,
        clients::delete_algo_proxy,
        clients::get_peer_stats_by_country,
        clients::get_peer_stats_by_country,
        clients::update_proxy_acc,
        clients::delete_proxy_acc,
        clients::create_proxy_acc,
        clients::get_proxy_acc_data,
        clients::get_client_overview,
        clients::get_client_usage_history,
        location::get_bonus_info,
        // location::get_continents,
        // location::get_countries,
        // location::get_countries_by_continent,
        masternode::register_masternode,
        masternode::deregister_masternode,
        masternode::assign_masternode,
        metrics::health,
        metrics::online_peers,
        metrics::online_masternodes,
        metrics::masternode_metrics,
        users::start_session,
        users::end_session,
        users::heartbeat,
        users::get_total_time,
        clients::force_stop_peer,
        clients::health_check_peer,
    ),
    components(
        schemas(
            dpn_core::types::auth::UserClaims,
            dpn_core::types::auth::UserSignUpReq,
            dpn_core::types::auth::UserSignUpResp,
            dpn_core::types::auth::UserSignInReq,
            dpn_core::types::auth::SSOInfo,
            dpn_core::types::auth::GoogleSSOInfo,
            dpn_core::types::auth::AppleSSOInfo,
            dpn_core::types::auth::SSORes,
            dpn_core::types::auth::AuthTokens,
            dpn_core::types::connection::PeernodeInfo,
            SessionDetails,
            connections::BandwidthPrice,
            UserBandwidthPrice,
            Continent,
            Country,
            City,
            MasternodeInfo,
            RewardsOverview,
            users::WithdrawalHistoryDto,
            users::UpdateWithdrawalAddressRequest,
            users::CreateReferralCodeRequest,
            users::UserDetailDto,
            users::UserXpDto,
            users::ReferralDto,
            users::UserDto,
            users::TotalPendingWithdrawalTxsDto,
            users::NotificationRegisterReq,
            users::UpdateUserOnlineSession,
            users::LoyaltyPointsRequest,
            users::LoyaltyPointsRequest,
            TxStatus,
            TxType,
            Tier,
            UserTier,
            TierPoint,
            referral::ReferralsOverview,
            AssignMasternodeRes,
            ProxyAccDto,
            VerifyProxyAccData,
            BandwidthPrice,
            ErrorWrapper,
            ConnectionOverview,
            SessionStatus,
            RegionInfoResponse,
            UserBandwidthPrice,
            UserRegionInfo,
            ProxyAccUpdate,
            ProxyAccInfo,
            ProxyAccDelete,
            PrioritizedIPLevel,
            UserRegionInfoHistory,
            MasternodeMetric,
            ClientOverview,
            OnlinePeerByGeo,
            PeerByCountryStats,
            ForceStopPeerRequest,
            HealthCheckResponse,
        )
    ),
    tags(
        (name = "DPN ADMIN REST API", description = "DPN Admin Endpoints")
    ),
    modifiers(&SecurityAddon)
)]
pub struct ApiDoc;

pub struct SecurityAddon;

impl Modify for SecurityAddon {
    fn modify(&self, openapi: &mut utoipa::openapi::OpenApi) {
        let components = openapi.components.as_mut().unwrap();
        components.add_security_scheme(
            "Bearer",
            SecurityScheme::Http(
                HttpBuilder::new()
                    .scheme(HttpAuthScheme::Bearer)
                    .bearer_format("JWT")
                    .build(),
            ),
        );
        components.add_security_scheme(
            "x-api-key",
            SecurityScheme::ApiKey(ApiKey::Header(ApiKeyValue::new("x-api-key".to_string()))),
        );
    }
}
