use crate::webapi::{AppState, UserDetailCache};
use actix_web::{get, post, web, HttpMessage, HttpRequest, HttpResponse, Result};
use dpn_core::types::api::ErrorWrapper;
use dpn_core::types::noti::NotificationRegister;
use dpn_core::{
    types::{
        auth::UserClaims,
        tier::UserTier,
        tx::{Tx, TxStatus, TxType},
    },
    utils::{bytes_to_hex_string, u256_to_szabo},
};
use ethers::types::Address;
use log::{debug, error};
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use serde_json::json;
use sqlx::types::chrono;
use std::sync::Arc;
use utoipa::ToSchema;
use tokio;

pub fn init_routes(cfg: &mut web::ServiceConfig) {
    // user
    cfg.service(get_user_detail);
    cfg.service(get_user_tier_points);

    // referral
    cfg.service(get_referral_by_me);
    cfg.service(create_referral_code);
    cfg.service(import_referral_code);
    cfg.service(get_referrals_overview_of);

    // rewards
    cfg.service(claim_rewards);
    cfg.service(claim_rewards_history);
    cfg.service(update_withdrawal_address);
    cfg.service(rewards_overview);
    cfg.service(get_total_pending_withdrawal_txs);

    // notifications
    cfg.service(register_notification);

    // loyalty points
    cfg.service(start_session);
    cfg.service(end_session);
    cfg.service(heartbeat);
    cfg.service(get_total_time);
}

#[derive(Deserialize, Serialize, Debug, ToSchema)]
pub struct CreateReferralCodeRequest {
    referral_code: String,
}

#[derive(Debug, Clone, Serialize, ToSchema)]
pub struct UserDetailDto {
    user: UserDto,
    user_tier: UserTier,
    user_referral: ReferralDto,
    user_xp: UserXpDto,
}

#[derive(Debug, Clone, Serialize, ToSchema)]
pub struct UserXpDto {
    pub minutes_uptime: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct RegionInfo {
    pub city_geoname_id: u32,
    pub country_geoname_id: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct UserDto {
    pub email: Option<String>,
    pub fingerprint: Option<String>,
    pub pincode: Option<String>,
    pub deposit_addr: String,
    pub withdrawal_addr: Option<String>,
    pub balance: i64,
    pub created_at: i64,
    pub last_login: i64,
}

#[utoipa::path(
    get,
    path = "/users/detail",
    tag = "Users",
    responses(
        (status=200, description= "Fetch user detail successfully!", body = UserDetailDto),
        (status=404, description= "User not found!", body=ErrorWrapper),
        (status=404, description= "Server returns unexpected error!", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/detail")]
pub async fn get_user_detail(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    // Try to get user details from cache first
    let cache_key = format!("user_detail:{}", user_addr);
    let current_time = chrono::Utc::now().timestamp();

    if let Some(cached_data) = app_state.user_detail_cache.get(&cache_key).await {
        // Use cached data if it exists and is not too old (5 minutes)
        let user = cached_data.user.clone();
        let user_tier = cached_data.user_tier.clone();
        let user_ref = cached_data.user_referral.clone();

        if let (Some(user), Some(user_tier), Some(user_ref)) = (user, user_tier, user_ref) {
            debug!("Using cached user detail for user: {}", user_addr);

            // Construct response from cache
            let response = UserDetailDto {
                user: UserDto {
                    email: user.email,
                    fingerprint: user.fingerprint,
                    pincode: user.pincode,
                    deposit_addr: bytes_to_hex_string(user.deposit_addr.as_bytes()),
                    withdrawal_addr: user
                        .withdrawal_addr
                        .map(|a| bytes_to_hex_string(a.as_bytes())),
                    balance: u256_to_szabo(user.balance),
                    created_at: user.created_at,
                    last_login: user.last_login,
                },
                user_tier,
                user_referral: ReferralDto {
                    user_addr: bytes_to_hex_string(user_ref.user_addr.as_bytes()),
                    referral_code: user_ref.referral_code.clone(),
                    created_at: user_ref.created_at,
                    referred_by: user_ref
                        .referred_by
                        .map(|x| bytes_to_hex_string(x.as_bytes())),
                    referred_at: user_ref.referred_at,
                    tx_hash: user_ref.tx_hash.map(|x| bytes_to_hex_string(x.as_bytes())),
                },
                user_xp: UserXpDto {
                    minutes_uptime: 0.0,
                },
            };

            return Ok(HttpResponse::Ok().json(response));
        }
    }

    // If not in cache or cache is invalid, fetch from database
    let (user, user_tier, user_ref) = tokio::join!(
        user_svc.clone().get_user_by_addr(user_addr.clone()),
        user_svc.clone().get_tier_of(user_addr.clone()),
        user_svc.clone().get_referral_of(user_addr.clone())
    );

    if let (Ok(Some(user)), Ok(user_tier), Ok(user_ref)) = (user, user_tier, user_ref) {
        // Store in cache for future requests
        app_state.user_detail_cache.insert(
            cache_key,
            UserDetailCache {
                user: Some(user.clone()),
                user_tier: Some(user_tier.clone()),
                user_referral: Some(user_ref.clone()),
                last_updated: current_time,
            }
        ).await;

        let minutes_uptime = 0.0;

        let response = UserDetailDto {
            user: UserDto {
                email: user.email,
                fingerprint: user.fingerprint,
                pincode: user.pincode,
                deposit_addr: bytes_to_hex_string(user.deposit_addr.as_bytes()),
                withdrawal_addr: user
                    .withdrawal_addr
                    .map(|a| bytes_to_hex_string(a.as_bytes())),
                balance: u256_to_szabo(user.balance),
                created_at: user.created_at,
                last_login: user.last_login,
            },
            user_tier,
            user_referral: ReferralDto {
                user_addr: bytes_to_hex_string(user_ref.user_addr.as_bytes()),
                referral_code: user_ref.referral_code.clone(),
                created_at: user_ref.created_at,
                referred_by: user_ref
                    .referred_by
                    .map(|x| bytes_to_hex_string(x.as_bytes())),
                referred_at: user_ref.referred_at,
                tx_hash: user_ref.tx_hash.map(|x| bytes_to_hex_string(x.as_bytes())),
            },
            user_xp: UserXpDto { minutes_uptime },
        };

        Ok(HttpResponse::Ok().json(response))
    } else {
        error!("not found user with id: {}", user_addr);
        Ok(ErrorWrapper::builder(StatusCode::INTERNAL_SERVER_ERROR, "user not found").build())
    }
}

#[utoipa::path(
    get,
    path = "/users/tier_points",
    tag = "Users",
    responses(
        (status=200, description= "Fetch user tier points successfully!", body = [TierPoint]),
        (status=500, description= "Server returns unexpected error!", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/tier_points")]
pub async fn get_user_tier_points(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    match user_svc
        .clone()
        .get_user_tier_points(user_addr.clone())
        .await
    {
        Ok(tier_points) => return Ok(HttpResponse::Ok().json(tier_points)),
        Err(e) => {
            error!(
                "cannot get user tier points id: {} err={}",
                user_addr.clone(),
                e
            );
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                "cannot get user tier points",
            )
            .build());
        }
    }
}

#[utoipa::path(
    post,
    path = "/users/referral/create",
    tag = "Users",
    request_body(content = CreateReferralCodeRequest, description = "Create Referral Code Request"),
    responses(
        (status=200, description= "Create referral code successfully!"),
        (status=400, description= "Failed to create referral code", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[post("/referral/create")]
pub async fn create_referral_code(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<CreateReferralCodeRequest>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    if (body.referral_code.len() < 6 || body.referral_code.len() > 16)
        || (!body
            .referral_code
            .chars()
            .all(|c| c.is_ascii_alphanumeric()))
    {
        return Ok(ErrorWrapper::builder(StatusCode::BAD_REQUEST, "invalid referral code").build());
    }

    match user_svc
        .create_referral_code_of(user_addr.clone(), body.referral_code.to_string())
        .await
    {
        Ok(_) => {
            // remove cache user detail 
            let cache_key = format!("user_detail:{}", user_addr);
            app_state.user_detail_cache.invalidate(&cache_key).await;
            return Ok(HttpResponse::Ok().into())
        },
        Err(e) => {
            error!("failed to create referral code {}", e);
            return Ok(ErrorWrapper::builder(StatusCode::BAD_REQUEST, &format!("{}", e)).build());
        }
    }
}

#[utoipa::path(
    post,
    path = "/users/referral/link",
    tag = "Users",
    request_body(content = CreateReferralCodeRequest, description = "Create Referral Link Request"),
    responses(
        (status=200, description= "Import referral code successfully!"),
        (status=400, description= "Failed to import referral code", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[post("/referral/link")]
pub async fn import_referral_code(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<CreateReferralCodeRequest>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    match user_svc
        .import_referral_code_of(user_addr.clone(), body.referral_code.to_string())
        .await
    {
        Ok(_) => Ok(HttpResponse::Ok().into()),
        Err(e) => {
            error!("import referral code with error: {}", e);
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                &format!("failed to import referral code: {}", e),
            )
            .build());
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
pub struct ReferralDto {
    pub user_addr: String,
    pub referral_code: Option<String>,
    pub created_at: i64,
    pub referred_by: Option<String>,
    pub referred_at: Option<i64>,
    pub tx_hash: Option<String>,
}

#[utoipa::path(
    get,
    path = "/users/referral/history",
    tag = "Users",
    responses(
        (status=200, description= "Fetch referral successfully!", body = [ReferralDto]),
        (status=400, description= "Not found referral user", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/referral/history")]
pub async fn get_referral_by_me(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    match user_svc.get_referrals_by(user_addr.clone()).await {
        Ok(referrals) => {
            let referrals: Vec<ReferralDto> = referrals
                .iter()
                .map(|r| ReferralDto {
                    user_addr: bytes_to_hex_string(r.user_addr.as_bytes()),
                    referral_code: r.referral_code.clone(),
                    created_at: r.created_at,
                    referred_by: r.referred_by.map(|x| bytes_to_hex_string(x.as_bytes())),
                    referred_at: r.referred_at,
                    tx_hash: r.tx_hash.map(|x| bytes_to_hex_string(x.as_bytes())),
                })
                .collect();
            Ok(HttpResponse::Ok().json(referrals))
        }
        Err(e) => {
            error!("cannot get referral user with error: {}", e);
            return Ok(
                ErrorWrapper::builder(StatusCode::BAD_REQUEST, "cannot get referral user").build(),
            );
        }
    }
}

#[utoipa::path(
    get,
    path = "/users/referral/overview",
    tag = "Users",
    responses(
        (status=200, description= "Fetch referral successfully!", body = ReferralsOverview),
        (status=404, description= "Not found user", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/referral/overview")]
pub async fn get_referrals_overview_of(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let accounting_svc = app_state.accounting_svc.clone();

    match accounting_svc
        .get_referral_overview(user_addr.clone())
        .await
    {
        Ok(result) => return Ok(HttpResponse::Ok().json(result)),
        Err(e) => {
            error!("cannot get referrals overview with error: {}", e);
            return Ok(ErrorWrapper::builder(StatusCode::BAD_REQUEST, &format!("{}", e)).build());
        }
    }
}

#[utoipa::path(
    post,
    path = "/users/rewards/claim",
    tag = "Users",
    responses(
        (status=201, description= "Sent withdrawal event successfully!"),
        (status=500, description= "Failed to send withdrawal event", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[post("/rewards/claim")]
pub async fn claim_rewards(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    let user_svc = app_state.user_svc.clone();
    match user_svc.get_user_by_addr(user_addr.clone()).await {
        Ok(Some(user)) => {
            if user.withdrawal_addr.is_none() {
                return Ok(ErrorWrapper::builder(
                    StatusCode::BAD_REQUEST,
                    "user has not set a withdrawal address",
                )
                .build());
            }
            match app_state
                .user_svc
                .clone()
                .withdraw_reward(
                    bytes_to_hex_string(user.deposit_addr.as_bytes()),
                    bytes_to_hex_string(user.withdrawal_addr.unwrap().as_bytes()),
                )
                .await
            {
                Ok(_) => Ok(HttpResponse::Ok().into()),
                Err(e) => {
                    return Ok(ErrorWrapper::builder(
                        StatusCode::INTERNAL_SERVER_ERROR,
                        &format!("failed to publish withdrawal event: {}", e),
                    )
                    .build());
                }
            }
        }
        _ => Ok(ErrorWrapper::builder(StatusCode::NOT_FOUND, "user not found").build()),
    }
}

#[utoipa::path(
    get,
    path = "/users/rewards/claim_history",
    tag = "Users",
    responses(
        (status=200, description= "Get claim history!", body = [WithdrawalHistoryDto]),
        (status=400, description= "Failed to fetch claim history", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/rewards/claim_history")]
pub async fn claim_rewards_history(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    let accounting_svc = app_state.accounting_svc.clone();

    match accounting_svc.get_withdrawal_history(user_addr).await {
        Ok(withdrawals) => {
            return Ok(HttpResponse::Ok().json(withdrawals).into());
        }
        Err(e) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                &format!("failed to fetch claim history err={}", e),
            )
            .build());
        }
    }
}

#[utoipa::path(
    post,
    path = "/users/rewards/update_withdrawal_address",
    tag = "Users",
    request_body(content = UpdateWithdrawalAddressRequest, description = "Update Withdrawal Address Request Body"),
    responses(
        (status=201, description= "Updated withdrawal address successfully!"),
        (status=400, description= "Failed to update withdrawal address", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[post("/rewards/update_withdrawal_address")]
pub async fn update_withdrawal_address(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<UpdateWithdrawalAddressRequest>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let rw_svc = app_state.user_svc.clone();

    match body.withdrawal_addr.parse::<Address>() {
        Ok(withdrawal_addr) => match rw_svc
            .update_withdrawal_addr_of(user_addr.clone(), withdrawal_addr)
            .await
        {
            Ok(_) => {
                // Invalidate the cache for this user
                let cache_key = format!("user_detail:{}", user_addr);
                app_state.user_detail_cache.invalidate(&cache_key).await;
                debug!("Invalidated cache for user: {}", user_addr);

                return Ok(HttpResponse::Created().into());
            },
            Err(_) => {
                return Ok(ErrorWrapper::builder(
                    StatusCode::BAD_REQUEST,
                    "failed to update withdrawal address",
                )
                .build());
            }
        },
        Err(_) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                "invalid format of withdrawal address",
            )
            .build());
        }
    }
}

#[derive(Debug, Clone, Serialize, ToSchema)]
pub struct RewardsOverviewDto {
    pub total_rewards: i64,
    pub unclaimed_rewards: i64,
    pub total_rewards_v2: i64,
    pub unclaimed_rewards_v2: i64,
    pub total_network_rewards: i64,
    pub total_network_rewards_v2: i64,
    pub total_task_rewards: i64,
    pub total_referral_rewards: i64,
    pub total_referral_rewards_v2: i64,
    pub total_commission_rewards: i64,
    pub minutes_uptime: f64,
}

#[utoipa::path(
    get,
    path = "/users/rewards/overview",
    tag = "Users",
    responses(
        (status=200, description= "Get overview reward successfully!", body = RewardsOverviewDto),
        (status=400, description= "Failed to fetch overview reward", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/rewards/overview")]
pub async fn rewards_overview(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let accounting_svc = app_state.accounting_svc.clone();
    let minutes_uptime = 0.0;

    match accounting_svc.get_reward_overview(user_addr).await {
        Ok(reward_overview) => {
            let reward_overview_dto = RewardsOverviewDto {
                total_rewards: reward_overview.total_rewards.clone(),
                unclaimed_rewards: reward_overview.unclaimed_rewards.clone(), 
                total_rewards_v2: reward_overview.total_rewards_v2.clone(),
                unclaimed_rewards_v2: reward_overview.unclaimed_rewards_v2.clone(), 
                total_network_rewards: reward_overview.total_network_rewards.clone(), 
                total_network_rewards_v2: reward_overview.total_network_rewards_v2.clone(),
                total_task_rewards: reward_overview.total_task_rewards.clone(),
                total_referral_rewards: reward_overview.total_referral_rewards.clone(), 
                total_referral_rewards_v2: reward_overview.total_referral_rewards_v2.clone(),
                total_commission_rewards: reward_overview.total_commission_rewards.clone(),
                minutes_uptime
            };
            return Ok(HttpResponse::Ok().json(reward_overview_dto))
        },
        Err(_) => {
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                "failed to get rewards overview",
            )
            .build());
        }
    }
}


#[utoipa::path(
    post,
    path = "/users/loyalty_points/start_session",
    tag = "Loyalty_points",
    request_body(content = LoyaltyPointsRequest),
    security(
        ("Bearer" = [])
    )
)]
#[post("/loyalty_points/start_session")]
pub async fn start_session(
    req: HttpRequest,
    body: web::Json<LoyaltyPointsRequest>,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let device_id = body.device_id.clone();
    let loyalty_points_svc = app_state.loyalty_points_svc.clone();

    match loyalty_points_svc.start_session(user_addr, device_id).await {
        Ok(_) => Ok(HttpResponse::Ok().into()),
        Err(e) => {
            error!("failed to start session: {}", e);
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                &format!("failed to start session: {}", e),
            )
            .build());
        }
    }
}
#[utoipa::path(
    post,
    path = "/users/loyalty_points/end_session",
    tag = "Loyalty_points",
    request_body(content = LoyaltyPointsRequest),
    security(
        ("Bearer" = [])
    )
)]
#[post("/loyalty_points/end_session")]
pub async fn end_session(
    req: HttpRequest,
    body: web::Json<LoyaltyPointsRequest>,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();

    let device_id = body.device_id.clone();
    let loyalty_points_svc = app_state.loyalty_points_svc.clone();

    match loyalty_points_svc.end_session(user_addr, device_id).await {
        Ok(_) => Ok(HttpResponse::Ok().into()),
        Err(e) => {
            error!("failed to end session: {}", e);
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                &format!("failed to end session: {}", e),
            )
            .build());
        }
    }
}

#[utoipa::path(
    post,
    path = "/users/loyalty_points/heartbeat",
    tag = "Loyalty_points",
    request_body(content = LoyaltyPointsRequest),
    security(
        ("Bearer" = [])
    )
)]
#[post("/loyalty_points/heartbeat")]
pub async fn heartbeat(
    req: HttpRequest,
    body: web::Json<LoyaltyPointsRequest>,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let device_id = body.device_id.clone();
    let loyalty_points_svc = app_state.loyalty_points_svc.clone();

    match loyalty_points_svc.heartbeat(user_addr, device_id).await {
        Ok(_) => Ok(HttpResponse::Ok().into()),
        Err(e) => {
            error!("failed to heartbeat: {}", e);
            return Ok(ErrorWrapper::builder(
                StatusCode::INTERNAL_SERVER_ERROR,
                &format!("failed to heartbeat: {}", e),
            )
            .build());
        }
    }
}

#[utoipa::path(
    get,
    path = "/users/loyalty_points/get_total_time",
    tag = "Loyalty_points",
    security(
        ("Bearer" = [])
    ),
    responses(
        (status=200, description= "Get total time successfully!", body = TotalTimeDto),
        (status=500, description= "failed to get total time", body = ErrorWrapper),
    ),
)]
#[get("/loyalty_points/get_total_time")]
pub async fn get_total_time(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let loyalty_points_svc = app_state.loyalty_points_svc.clone();

    match loyalty_points_svc.get_total_time(user_addr).await {
        Ok(total_time) => {
            Ok(HttpResponse::Ok().json(total_time))
        },
        Err(e) => {
            error!("failed to get total time: {}", e);
            return Ok(ErrorWrapper::builder(StatusCode::INTERNAL_SERVER_ERROR, "failed to get total time").build());
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct TotalTimeDto {
    pub total_time: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct LoyaltyPointsRequest {
    pub device_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct TotalPendingWithdrawalTxsDto {
    pub total_pending_withdrawal_txs: i64,
}

#[utoipa::path(
    get,
    path = "/users/rewards/get_total_pending_withdrawal_txs",
    tag = "Users",
    responses(
        (status=200, description= "Get overview reward successfully!", body = TotalPendingWithdrawalTxsDto),
        (status=500, description= "failed to get total pending txs", body = ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/rewards/get_total_pending_withdrawal_txs")]
pub async fn get_total_pending_withdrawal_txs(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    match user_svc
        .get_total_pending_withdrawal_txs_of(user_addr.clone())
        .await
    {
        Ok(result) => {
            let json_response = json!({
                "total_pending_withdrawal_txs": result
            });

            Ok(HttpResponse::Ok().json(json_response))
        }
        Err(e) => {
            error!("failed to get total pending txs: {}", e);
            return Ok(ErrorWrapper::builder(
                StatusCode::NOT_FOUND,
                "failed to get total pending txs",
            )
            .build());
        }
    }
}

#[derive(serde::Deserialize, Debug, ToSchema)]
pub struct NotificationRegisterReq {
    token: String,
    device_type: String,
}

#[utoipa::path(
    post,
    path = "/users/notification/register",
    tag = "Users",
    request_body(content = NotificationRegisterReq),
    responses(
        (status=200, description= "Register notification successfully!"),
        (status=500, description= "Server returns unexpected error!", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[post("/notification/register")]
pub async fn register_notification(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
    body: web::Json<NotificationRegisterReq>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    match user_svc.clone().get_user_by_addr(user_addr.clone()).await {
        Ok(Some(user)) => {
            let event = NotificationRegister {
                user_addr: user_addr.clone(),
                email: user.email.clone().unwrap_or_default(),
                token: body.token.clone(),
                device_type: body.device_type.clone(),
                login_session_id: user_claims.login_session_id.clone(),
            };
            match user_svc
                .register_notification(event)
                .await
            {
                Ok(_) => Ok(HttpResponse::Ok().into()),
                Err(e) => {
                    error!("failed to register notification: {}", e);
                    return Ok(ErrorWrapper::builder(
                        StatusCode::INTERNAL_SERVER_ERROR,
                        &format!("failed to register notification: {}", e),
                    )
                    .build());
                }
            }
        }
        Ok(None) => {
            return Ok(ErrorWrapper::builder(StatusCode::NOT_FOUND, "user not found").build());
        }
        Err(e) => {
            error!("not found user with id: {} err={}", user_addr, e);
            return Ok(
                ErrorWrapper::builder(StatusCode::INTERNAL_SERVER_ERROR, "internal server error").build(),
            );
        }
    }
}


#[utoipa::path(
    get,
    path = "/users/online-sessions",
    tag = "Users",
    responses(
        (status=200, description= "Get online sessions successfully!", body = [UserOnlineSession]),
        (status=500, description= "Server returns unexpected error!", body=ErrorWrapper),
    ),
    security(
        ("Bearer" = [])
    )
)]
#[get("/online-sessions")]
pub async fn get_online_sessions_of_user(
    req: HttpRequest,
    app_state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let user_claims = req.extensions().get::<UserClaims>().unwrap().clone();
    let user_addr = user_claims.user_addr.clone();
    let user_svc = app_state.user_svc.clone();

    match user_svc.clone().get_online_sessions_of_user(user_addr.clone()).await {
        Ok(online_sessions) => Ok(HttpResponse::Ok().json(online_sessions)),
        Err(e) => {
            error!("failed to get online sessions of user: {}", e);
            return Ok(ErrorWrapper::builder(
                StatusCode::BAD_REQUEST,
                &format!("failed to get online sessions of user: {}", e),
            )
            .build());
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct UserOnlineSession {
    pub user_addr: String,
    pub earned_lp: Option<f64>,
    pub start_time: i64,
    pub end_time: Option<i64>,
    pub created_at: Option<i64>,
    pub updated_at: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserOnlineSessionRedisKey {
    pub user_addr: String,
    pub start_time: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserOnlineSessionRedis {
    pub start_time: i64,
    pub updated_at: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct UpdateUserOnlineSession {
    pub start_time: i64,
}

#[derive(serde::Deserialize, Debug, ToSchema)]
pub struct UpdateWithdrawalAddressRequest {
    withdrawal_addr: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct WithdrawalHistoryDto {
    pub tx_hash: String,
    pub from_addr: String,
    pub to_addr: String,
    pub amount: i64,
    pub tx_type: TxType,
    pub tx_status: TxStatus,
    pub chain_tx_hash: Option<String>,
    pub created_at: i64,
}

impl Into<WithdrawalHistoryDto> for Tx {
    fn into(self) -> WithdrawalHistoryDto {
        WithdrawalHistoryDto {
            tx_hash: bytes_to_hex_string(self.tx_hash.as_bytes()),
            from_addr: bytes_to_hex_string(self.from_addr.as_bytes()),
            to_addr: bytes_to_hex_string(self.to_addr.as_bytes()),
            amount: u256_to_szabo(self.amount),
            tx_type: self.tx_type,
            tx_status: self.tx_status,
            chain_tx_hash: self
                .chain_tx_hash
                .map(|t| bytes_to_hex_string(t.as_bytes())),
            created_at: self.created_at,
        }
    }
}